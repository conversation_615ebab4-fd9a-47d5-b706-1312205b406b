"use client";
import Card<PERSON><PERSON>ground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";

import CardAttribute from "@/components/CardElement-components/card-elements/CardAttribute";
import CardCost from "@/components/CardElement-components/card-elements/CardCost";

import CardPrintWave from "@/components/CardElement-components/card-elements/CardPrintWave";
import CardRarity from "@/components/CardElement-components/card-elements/CardRarity";

import CardName from "@/components/CardElement-components/card-elements/CardName";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardType from "@/components/CardElement-components/card-elements/CardType";
import CardPower from "@/components/CardElement-components/card-elements/CardPower";

import CardSetAndNum from "@/components/CardElement-components/card-elements/CardSetAndNum";
import CardRarityText from "@/components/CardElement-components/card-elements/CardRarityText";
import CardPrintWaveText from "@/components/CardElement-components/card-elements/CardPrintWaveText";
import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";
import Card from "@/components/CardElement-components/Card";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import LeaderCardBorderBottom from "@/components/CardElement-components/card-elements/LeaderCardBorderBottom";
import CardAbilityLeader from "@/components/CardElement-components/card-elements/CardAbilityLeader";
import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";
import React from "react";

export default function GenerateLeader({ CMW = false }: { CMW: boolean }) {
  const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");
  const printReady = useGetStoreState("printReady");
  return (
    <Card maxWPx={printReady ? 3677 : 3357}>
      <CardBackground cardType={"leader"} quality={100} png={true} />
      <img
        className={
          "card-background-image absolute z-0 h-full w-full object-cover"
        }
        alt={""}
      />
      <CardBorder
        cardType={"leader"}
        quality={100}
        png={true}
        absolute={true}
      />

      <img
        className={"cardScreenshot absolute z-3 h-full w-full object-cover"}
        alt={""}
      />
      <LeaderCardBorderBottom cardType={"leader"} quality={100} png={true} />
      {leaderBorderEnabled && (
        <CardBorder
          cardType={"leader"}
          quality={100}
          png={true}
          absolute={true}
          zIndex={4}
        />
      )}
      <CardAttribute cardKind={"leader"} />
      <CardCost leader={true} />
      <CardPrintWave
        quality={100}
        cardType={"leader"}
        cardKindRoute={"leader"}
      />
      <CardRarity quality={100} cardType={"leader"} cardKindRoute={"leader"} />
      <CardAbilityLeader />
      <CardName cardType={"leader"} />
      <CardKind cardType={"leader"} />
      <CardType cardType={"leader"} />
      <CardPower cardType={"leader"} />
      <CardSetAndNum cardType={"leader"} />
      <CardRarityText cardType={"leader"} />
      <CardPrintWaveText cardType={"leader"} />
      <CardArtistText cardType={"leader"} />
      {CMW && <CardMadeWith cardType={"leader"} />}
      <CardColorWheel cardKind={"leader"} png={true} />
    </Card>
  );
}
