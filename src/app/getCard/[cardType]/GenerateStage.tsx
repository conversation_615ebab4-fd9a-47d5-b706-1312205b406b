"use client";
import Card<PERSON>ackground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";

import CardCost from "@/components/CardElement-components/card-elements/CardCost";

import CardPrintWave from "@/components/CardElement-components/card-elements/CardPrintWave";
import CardRarity from "@/components/CardElement-components/card-elements/CardRarity";

import CardName from "@/components/CardElement-components/card-elements/CardName";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardType from "@/components/CardElement-components/card-elements/CardType";

import CardSetAndNum from "@/components/CardElement-components/card-elements/CardSetAndNum";
import CardRarityText from "@/components/CardElement-components/card-elements/CardRarityText";
import CardPrintWaveText from "@/components/CardElement-components/card-elements/CardPrintWaveText";
import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";
import Card from "@/components/CardElement-components/Card";
import CardAbilityAndTrigger from "@/components/CardElement-components/card-elements/CardAbilityAndTrigger";
import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";
import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";

export default function GenerateStage({ CMW = false }: { CMW: boolean }) {
  const printReady = useGetStoreState("printReady");
  return (
    <Card maxWPx={printReady ? 3677 : 3357}>
      <CardBackground cardType={"stage"} quality={100} png={true} />
      <img
        className={
          "card-background-image absolute z-[-1] h-full w-full object-cover"
        }
        alt={""}
      />
      <img
        className={"cardScreenshot absolute z-0 h-full w-full object-cover"}
        alt={""}
      />
      <CardBorder cardType={"stage"} quality={100} png={true} zIndex={4} />
      <CardCost eventOrStage={true} />

      <CardPrintWave quality={100} cardKindRoute={"stage"} />
      <CardRarity quality={100} cardType={"standard"} cardKindRoute={"stage"} />
      <CardAbilityAndTrigger
        abilityBackGround={false}
        triggerQuality={100}
        cardType={"stage"}
      />
      <CardName cardType={"stage"} />
      <CardKind cardType={"stage"} />
      <CardType cardType={"stage"} />

      <CardSetAndNum cardType={"stage"} />
      <CardRarityText cardType={"stage"} />
      <CardPrintWaveText cardType={"stage"} />
      <CardArtistText cardType={"stage"} />
      {CMW && <CardMadeWith cardType={"stage"} />}
      <CardColorWheel cardKind={"stage"} png={true} />
    </Card>
  );
}
