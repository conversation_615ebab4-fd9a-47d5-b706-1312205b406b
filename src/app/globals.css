@import 'tailwindcss';

@import "tw-animate-css";

/*---break---*/
@custom-variant dark (&:is(.dark *));

@theme {
    --animate-pulse-fast: pulse 1.5s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    --text-xxl: 2rem;
    --breakpoint-3xl: 130rem;
  /*--background-image-gradient-radial: radial-gradient(var(--tw-gradient-stops));*/
  /*--background-image-gradient-conic: conic-gradient(*/
  /*  from 180deg at 50% 50%,*/
  /*  var(--tw-gradient-stops)*/
  /*);*/
    --color-background: var(--background);
    --color-full-button: oklch(0.14 0.00 0);
    --color-full-button-lite-dark: oklch(0.205 0 0);
    --color-full-button-hover: oklch(1 0 0 / 20%);
    --color-full-button-active: oklch(1 0 0 / 10%);
    --color-full-button-dark: oklch(0.97 0.00 0);
    --color-input-dark: oklch(0.14 0.00 0);
    /*--color-input-dark: #242424;*/
    --color-input-lite-dark: oklch(0.205 0 0);
    --color-full-button-hover-light: oklch(0.87 0.00 0);
    --color-full-button-active-light: oklch(0.72 0.00 0);
    --font-character-power: Character_Power, sans-serif;
    --font-event-cost: Event_Cost, sans-serif;
    --font-character-power-10k: Character_Power_10k, sans-serif;
    --font-balboa-plus: BalboaPlus, sans-serif;
    --font-balboa-plus-fill: BalboaPlusFill, sans-serif;
    --font-geist-sans: GeistSans, sans-serif;
    --font-one-piece: OnePiece, sans-serif;
    --font-one-piece-italic: OnePieceItalic, sans-serif;
    --font-one-piece-italic-bold: OnePieceItalicBold, sans-serif;
    --font-one-piece-rarity: OnePieceRarity, sans-serif;
    --font-leader-power: LeaderPower, sans-serif;
    --font-pixymbols: PIXymbols, sans-serif;
    --font-roboto: Roboto, sans-serif;
    --font-geologica: Geologica, sans-serif;
    --font-wix: Wix Madefor Display, sans-serif;
    --font-mohave: Alumni Sans, sans-serif;
    --font-hind-siliguri: Hind Siliguri, sans-serif;
    --font-libre-franklin: Libre Franklin, sans-serif;
    --font-roboto-mono: Roboto Mono, monospace;
    --font-roboto-condensed: Roboto Condensed, sans-serif;
    --font-commissioner: Commissioner, sans-serif;
    --font-mukta: Mukta Malar, sans-serif;
    --font-open-sans: Open Sans, sans-serif;
}

/*
  The default border color has changed to `currentcolor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentcolor);
  }
}
#tippy-2 {
    z-index: 10 !important;
}
/* Character Power fonts (already existed) */
@font-face {
    font-family: "Character_Power";
    src: url('https://r2.ultimatetcgcm.com/OnePieceTcg_power-Regular.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "Event_Cost";
    src: url('https://r2.ultimatetcgcm.com/Brotherland_Signature.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "Character_Power_10k";
    src: url('https://r2.ultimatetcgcm.com/Character_power_10k-Regular.ttf') format("truetype");
    font-display: swap;
}

/* Added local fonts as CDN fonts */
@font-face {
    font-family: "BalboaPlus";
    src: url('https://r2.ultimatetcgcm.com/BalboaPlus-W00-Inline.woff2') format("woff2");
    font-display: swap;
}

@font-face {
    font-family: "BalboaPlusFill";
    src: url('https://r2.ultimatetcgcm.com/balboaplus-fill-webfont.woff2') format("woff2");
    font-display: swap;
}

@font-face {
    font-family: "GeistSans";
    src: url('https://r2.ultimatetcgcm.com/GeistVariableVF.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "OnePiece";
    src: url('https://r2.ultimatetcgcm.com/OnePieceTcg-Regular.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "OnePieceItalic";
    src: url('https://r2.ultimatetcgcm.com/One-piece-italic.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "OnePieceItalicBold";
    src: url('https://r2.ultimatetcgcm.com/One-piece-italic.ttf') format("truetype");
    font-weight: bold;
    font-display: swap;
}

@font-face {
    font-family: "OnePieceRarity";
    src: url('https://r2.ultimatetcgcm.com/Nimbus-Sans-TW01.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "CharacterPower";
    src: url('https://r2.ultimatetcgcm.com/OnePieceTcg_power-Regular.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "CharacterPower10k";
    src: url('https://r2.ultimatetcgcm.com/Character_power_10k-Regular.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "LeaderPower";
    src: url('https://r2.ultimatetcgcm.com/Leader_power-Regular.ttf') format("truetype");
    font-display: swap;
}

@font-face {
    font-family: "PIXymbols";
    src: url('https://r2.ultimatetcgcm.com/PIXymbols-fixed.woff2') format("woff2");
    font-display: swap;
}

/* Google fonts replaced with local fonts */
/* Roboto - Variable Font */
@font-face {
    font-family: "Roboto";
    src: url('https://r2.ultimatetcgcm.com/fonts/Roboto-VariableFont_wdth,wght.ttf') format("truetype-variations");
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

/* Geologica - Variable Font */
@font-face {
    font-family: "Geologica";
    src: url('https://r2.ultimatetcgcm.com/fonts/Geologica-VariableFont_CRSV,SHRP,slnt,wght.ttf') format("truetype-variations");
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

/* Wix Madefor Display */
@font-face {
    font-family: "Wix Madefor Display";
    src: url('https://r2.ultimatetcgcm.com/fonts/WixMadeforDisplay-VariableFont_wght.ttf') format("truetype");
    font-weight: 400 700;
    font-style: normal;
    font-display: swap;
}

/* Alumni Sans */
@font-face {
    font-family: "Alumni Sans";
    src: url('https://r2.ultimatetcgcm.com/fonts/AlumniSans-VariableFont_wght.ttf') format("truetype");
    font-weight: 300 700;
    font-style: normal;
    font-display: swap;
}

/* Hind Siliguri */
@font-face {
    font-family: "Hind Siliguri";
    src: url('https://r2.ultimatetcgcm.com/fonts/HindSiliguri-Light.ttf') format("truetype");
    font-weight: 300;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Hind Siliguri";
    src: url('https://r2.ultimatetcgcm.com/fonts/HindSiliguri-Regular.ttf') format("truetype");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Hind Siliguri";
    src: url('https://r2.ultimatetcgcm.com/fonts/HindSiliguri-Medium.ttf') format("truetype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Hind Siliguri";
    src: url('https://r2.ultimatetcgcm.com/fonts/HindSiliguri-Bold.ttf') format("truetype");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

/* Libre Franklin */
@font-face {
    font-family: "Libre Franklin";
    src: url('https://r2.ultimatetcgcm.com/fonts/LibreFranklin-VariableFont_wght.ttf') format("truetype");
    font-weight: 300 700;
    font-style: normal;
    font-display: swap;
}

/* Roboto Mono - Variable Font */
@font-face {
    font-family: "Roboto Mono";
    src: url('https://r2.ultimatetcgcm.com/fonts/RobotoMono-VariableFont_wght.ttf') format("truetype-variations");
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

/* Roboto Condensed - Variable Font */
@font-face {
    font-family: "Roboto Condensed";
    src: url('https://r2.ultimatetcgcm.com/fonts/RobotoCondensed-VariableFont_wght.ttf') format("truetype-variations");
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

/* Commissioner - Variable Font */
@font-face {
    font-family: "Commissioner";
    src: url('https://r2.ultimatetcgcm.com/fonts/Commissioner-VariableFont_wght.ttf') format("truetype-variations");
    font-weight: 100 900;
    font-style: normal;
    font-display: swap;
}

/* Mukta Malar */
@font-face {
    font-family: "Mukta Malar";
    src: url('https://r2.ultimatetcgcm.com/fonts/MuktaMalar-Regular.ttf') format("truetype");
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Mukta Malar";
    src: url('https://r2.ultimatetcgcm.com/fonts/MuktaMalar-Medium.ttf') format("truetype");
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Mukta Malar";
    src: url('https://r2.ultimatetcgcm.com/fonts/MuktaMalar-SemiBold.ttf') format("truetype");
    font-weight: 600;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Mukta Malar";
    src: url('https://r2.ultimatetcgcm.com/fonts/MuktaMalar-Bold.ttf') format("truetype");
    font-weight: 700;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: "Mukta Malar";
    src: url('https://r2.ultimatetcgcm.com/fonts/MuktaMalar-ExtraBold.ttf') format("truetype");
    font-weight: 800;
    font-style: normal;
    font-display: swap;
}

/* Open Sans - Variable Font */
@font-face {
    font-family: "Open Sans";
    src: url('https://r2.ultimatetcgcm.com/fonts/OpenSans-VariableFont_wdth,wght.ttf') format("truetype-variations");
    font-weight: 300 800;
    font-style: normal;
    font-display: swap;
}
/*COLOR THEME*/
:root {
    --radius: 0.625rem;
    --card: oklch(1 0 0);
    --card-foreground: oklch(0.145 0 0);
    --popover: oklch(1 0 0);
    /*--popover:   oklch(0.97 0 0);*/
    --popover-foreground: oklch(0.145 0 0);
    --primary: oklch(0.205 0 0);
    --primary-foreground: oklch(0.985 0 0);
    --secondary: oklch(0.97 0 0);
    --secondary-foreground: oklch(0.205 0 0);
    --muted: oklch(0.97 0 0);
    --muted-foreground: oklch(0.556 0 0);
    --accent: oklch(0.97 0 0);
    --accent-foreground: oklch(0.205 0 0);
    --destructive: oklch(0.577 0.245 27.325);
    --border: oklch(0.922 0 0);
    --input: oklch(0.922 0 0);
    --ring: oklch(0.708 0 0);
    --chart-1: oklch(0.646 0.222 41.116);
    --chart-2: oklch(0.6 0.118 184.704);
    --chart-3: oklch(0.398 0.07 227.392);
    --chart-4: oklch(0.828 0.189 84.429);
    --chart-5: oklch(0.769 0.188 70.08);
    --sidebar: oklch(0.985 0 0);
    --sidebar-foreground: oklch(0.145 0 0);
    --sidebar-primary: oklch(0.205 0 0);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.97 0 0);
    --sidebar-accent-foreground: oklch(0.205 0 0);
    --sidebar-border: oklch(0.922 0 0);
    --sidebar-ring: oklch(0.708 0 0);
    --background: oklch(1 0 0);
    --foreground: oklch(0.145 0 0);
}

.dark {
    --background: oklch(0.145 0 0);
    --foreground: oklch(0.985 0 0);
    --card: oklch(0.205 0 0);
    --card-foreground: oklch(0.985 0 0);
    --popover: oklch(0.205 0 0);
    --popover-foreground: oklch(0.985 0 0);
    --primary: oklch(0.922 0 0);
    --primary-foreground: oklch(0.205 0 0);
    --secondary: oklch(0.269 0 0);
    --secondary-foreground: oklch(0.985 0 0);
    --muted: oklch(0.269 0 0);
    --muted-foreground: oklch(0.708 0 0);
    --accent: oklch(0.269 0 0);
    --accent-foreground: oklch(0.985 0 0);
    --destructive: #7F1D1E;
    --border: oklch(1 0 0 / 10%);
    --input: oklch(1 0 0 / 15%);
    --ring: oklch(0.556 0 0);
    --chart-1: oklch(0.488 0.243 264.376);
    --chart-2: oklch(0.696 0.17 162.48);
    --chart-3: oklch(0.769 0.188 70.08);
    --chart-4: oklch(0.627 0.265 303.9);
    --chart-5: oklch(0.645 0.246 16.439);
    --sidebar: oklch(0.205 0 0);
    --sidebar-foreground: oklch(0.985 0 0);
    --sidebar-primary: oklch(0.488 0.243 264.376);
    --sidebar-primary-foreground: oklch(0.985 0 0);
    --sidebar-accent: oklch(0.269 0 0);
    --sidebar-accent-foreground: oklch(0.985 0 0);
    --sidebar-border: oklch(1 0 0 / 10%);
    --sidebar-ring: oklch(0.556 0 0);
}

:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 244, 244, 245;
    --background-end-rgb: 244, 244, 245;
    --_root-bd: 0,0,0;
    /*--primary: #171717;*/
    /*--primary-foreground: #FAFAFA;*/
}

@media (prefers-color-scheme: dark) {
    :root {
        --foreground-rgb: 212, 212, 212;
        --background-start-rgb: 24, 24, 27;
        --background-end-rgb: 24, 24, 27;
        /*--primary: #FAFAFA;*/
        /*--primary-foreground: #171717;*/
    }

}

body {
    -webkit-tap-highlight-color: transparent;
    /*REMOVE THIS FOR SHADCN*/
    color: rgb(var(--foreground-rgb));
    background: linear-gradient(
            to bottom,
            rgb(var(--background-start-rgb)),
            rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
    /*REMOVE THIS FOR SHADCN*/
    /*margin: 0; !* 1 *!*/
    font-family: var(--font-geist-sans), sans-serif;
    line-height: inherit; /* 2 */
    overflow-x: hidden;
}
/*.mantine-RichTextEditor-root {*/
/*    border: calc(0.0625rem*1) solid rgb(115, 115, 115)!important;*/
/*    color: rgb(212, 212, 212)*/
/*}*/
/*.mantine-RichTextEditor-toolbar {*/
/*    background-color: rgb(38, 38, 38)!important;*/
/*    border-bottom: calc(0.0625rem*1) solid rgb(115, 115, 115)!important;*/

/*}*/
/*.mantine-RichTextEditor-content {*/
/*    background-color: rgb(38, 38, 38)!important;*/
/*}*/
/*.mantine-RichTextEditor-control {*/
/*    background-color: rgb(38, 38, 38)!important;*/
/*    border: calc(0.0625rem*1) solid rgb(115, 115, 115)!important;*/

/*}*/
/*.mantine-RichTextEditor-controlsGroup {*/
/*    background-color: rgb(38, 38, 38)!important;*/
/*}.mantine-RichTextEditor-linkEditorDropdown {*/
/*    background-color: rgb(38, 38, 38)!important;*/
/*}*/
/* .mantine-InputWrapper-description {*/
/*     color: rgb(115, 115, 115);*/
/* }*/
/*.mantine-Input-input , .mantine-Input-section , .mantine-Input-wrapper ,.mantine-Select-section, .mantine-Select-input, .mantine-Select-dropdown  {*/
/*    background-color: rgb(38, 38, 38)!important;*/
/*    color: white;*/
/*}*/
/*.mantine-TextInput-input::placeholder {*/
/*    color: white;*/
/*}*/
.contentEditable {
    height: 500px;
    width: 100%;
}
/*.ability-input {*/
/*    font-size: 1rem;*/
/*}*/

::-webkit-scrollbar {
    width: 10px; /* Adjust the width as needed */
}

/* Thumb (the draggable part of the scrollbar) */
::-webkit-scrollbar-thumb {
    background-color: #a8a29e; /* Change the color as desired */
    border-radius: 5px; /* Adjust the border radius as needed */
}
@media (max-width: 1500px) {
    ::-webkit-scrollbar {
        width: 5px; /* Adjust the width as needed */
    }

    /* Thumb (the draggable part of the scrollbar) */
    ::-webkit-scrollbar-thumb {
        background-color: #a8a29e; /* Change the color as desired */
        border-radius: 5px; /* Adjust the border radius as needed */
        position: fixed;
    }
}
/*::-webkit-scrollbar-track {*/
/*    background: transparent;*/
/*}*/
/*!* Style the part of the track not covered by the thumb *!*/
/*::-webkit-scrollbar-track-piece {*/
/*    background: transparent;*/
/*}*/

/*!* Style the bottom button (if visible) *!*/
/*::-webkit-scrollbar-button:vertical:increment {*/
/*    background: transparent;*/
/*    height: 16px;*/
/*}*/
/*::-webkit-scrollbar-corner {*/
/*    background: transparent;*/
/*    border: 1px solid transparent;*/
/*}*/

 .reactEasyCrop_Image {
     max-width: none;
 }


.mantine-Button-root {
    font-weight: 400;
}

.bg-img {
    background-image: url("https://r2.ultimatetcgcm.com/assets/bg.png");
    background-size: 100px 100px;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    mask-image: linear-gradient(to bottom, transparent 1%, white 90%, transparent);
    z-index: -2;
}
@media (prefers-color-scheme: light) {

    .bg-img::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);

        z-index: -1;
    }
}
.bg-head {
     mask-image: radial-gradient(rgba(0, 0, 0, 1), transparent 70%);
     background: linear-gradient(83.21deg,  #a5f3fc 0%,  #fecdd3 100%);
     @media (prefers-color-scheme: dark) {
     mask-image: radial-gradient(rgba(0, 0, 0, 1), transparent 70%);
     background: linear-gradient(83.21deg,  #2563eb 0%,  #b91c1c 100%);
     }
     bottom: 10%;
     content: "";
     left: -25%;
     position: absolute;
     top: 0%;
     width: 150%;
     z-index: -1;
}
.main-page {
    mask-image: radial-gradient(rgba(0, 0, 0, 1), transparent 70%);
}
/*bg-gradient-to-r from-[#3245ff_0%] to-[#bc52ee_100%]*/
#utcgcmwm {
    background-image: url('https://r2.ultimatetcgcm.com/assets/watermark-white.webp');
    background-size: contain;
    background-repeat: no-repeat;
    height: 100%;
    width: 100%;
    position: absolute;
    opacity: 0.1;
    mix-blend-mode: difference; /* This will invert colors relative to background */
}

.text-stroke {
    text-shadow: -0.05em -0.05em 0 white, 0.05em -0.05em 0 white, -0.05em 0.05em 0 white, 0.05em 0.05em 0 white;
}

.red-button {
    /*background-color: #c02942; !* Darker red for the button *!*/

    color: white;

    height: 100%;

    border: none;
    border-radius: 90px;
    cursor: pointer;

    /*box-shadow: rgba(0, 0, 0, 0.4) 0px 31px 1px -28px inset,  rgba(0, 0, 0, 0.4) 20px 24px 1px -27px inset,  rgba(0, 0, 0, 0.4) 28px 12px 1px -27px inset, rgba(0, 0, 0, 0.4) 21px -20px 1px -27px inset,rgba(0, 0, 0, 1) 0px -22px 10px -28px inset, rgba(0, 0, 0, 1) -22px 0px 10px -28px inset,rgba(0, 0, 0, 0.4) -21px 20px 1px -27px inset,rgba(0, 0, 0, 0.1) 0 55px 1px -27px inset;*/

}

@media (prefers-color-scheme: dark) {
   .scroll-to-top {
       background: #fafafa !important;
   }
}


.image-container  {
    filter: drop-shadow(-0.03em -0.03em red) drop-shadow(-0.05em 0.05em  red) drop-shadow(0.07em -0.07em  red) drop-shadow(0.1em 0.1em  red);

}
/*New feature */



/*SHADCN*/
/*---break---*/

/*@layer base {*/
/*  :root {*/
/*        --background: 0 0% 100%;*/
/*        --foreground: 0 0% 3.9%;*/
/*        --card: 0 0% 100%;*/
/*        --card-foreground: 0 0% 3.9%;*/
/*        --popover: 0 0% 100%;*/
/*        --popover-foreground: 0 0% 3.9%;*/
/*        --primary: 0 0% 9%;*/
/*        --primary-foreground: 0 0% 98%;*/
/*        --secondary: 0 0% 96.1%;*/
/*        --secondary-foreground: 0 0% 9%;*/
/*        --muted: 0 0% 96.1%;*/
/*        --muted-foreground: 0 0% 45.1%;*/
/*        --accent: 0 0% 96.1%;*/
/*        --accent-foreground: 0 0% 9%;*/
/*        --destructive: 0 84.2% 60.2%;*/
/*        --destructive-foreground: 0 0% 98%;*/
/*        --border: 0 0% 89.8%;*/
/*        --input: 0 0% 89.8%;*/
/*        --ring: 0 0% 3.9%;*/
/*        --chart-1: 12 76% 61%;*/
/*        --chart-2: 173 58% 39%;*/
/*        --chart-3: 197 37% 24%;*/
/*        --chart-4: 43 74% 66%;*/
/*        --chart-5: 27 87% 67%;*/
/*        --radius: 0.5rem}*/
/*  .dark {*/
/*        --background: 0 0% 3.9%;*/
/*        --foreground: 0 0% 98%;*/
/*        --card: 0 0% 3.9%;*/
/*        --card-foreground: 0 0% 98%;*/
/*        --popover: 0 0% 3.9%;*/
/*        --popover-foreground: 0 0% 98%;*/
/*        --primary: 0 0% 98%;*/
/*        --primary-foreground: 0 0% 9%;*/
/*        --secondary: 0 0% 14.9%;*/
/*        --secondary-foreground: 0 0% 98%;*/
/*        --muted: 0 0% 14.9%;*/
/*        --muted-foreground: 0 0% 63.9%;*/
/*        --accent: 0 0% 14.9%;*/
/*        --accent-foreground: 0 0% 98%;*/
/*        --destructive: 0 62.8% 30.6%;*/
/*        --destructive-foreground: 0 0% 98%;*/
/*        --border: 0 0% 14.9%;*/
/*        --input: 0 0% 14.9%;*/
/*        --ring: 0 0% 83.1%;*/
/*        --chart-1: 220 70% 50%;*/
/*        --chart-2: 160 60% 45%;*/
/*        --chart-3: 30 80% 55%;*/
/*        --chart-4: 280 65% 60%;*/
/*        --chart-5: 340 75% 55%}}*/

/*!*---break---*!*/

/*@layer base {*/
/*  * {*/
/*    @apply border-border;}*/
/*  body {*/
/*    @apply bg-background text-foreground;}}*/
/*---break---*/
@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}
/*---break---*/
@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

