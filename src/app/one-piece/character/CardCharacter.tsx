import CardBackground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";
import CardAttribute from "@/components/CardElement-components/card-elements/CardAttribute";
import CardCost from "@/components/CardElement-components/card-elements/CardCost";

import CardPrintWave from "@/components/CardElement-components/card-elements/CardPrintWave";
import CardRarity from "@/components/CardElement-components/card-elements/CardRarity";

import CardName from "@/components/CardElement-components/card-elements/CardName";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardType from "@/components/CardElement-components/card-elements/CardType";
import CardPower from "@/components/CardElement-components/card-elements/CardPower";

import CardCounterText from "@/components/CardElement-components/card-elements/CardCounterText";
import CardCounter from "@/components/CardElement-components/card-elements/CardCounter";
import CardSetAndNum from "@/components/CardElement-components/card-elements/CardSetAndNum";
import CardRarityText from "@/components/CardElement-components/card-elements/CardRarityText";
import CardPrintWaveText from "@/components/CardElement-components/card-elements/CardPrintWaveText";
import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";

import CardAbilityAndTrigger from "@/components/CardElement-components/card-elements/CardAbilityAndTrigger";
import React from "react";

import CardWaterMark from "@/components/CardElement-components/CardWaterMark";

import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";
import CharacterCardBorderBottom from "@/components/CardElement-components/card-elements/CharacterCardBorderBottom";

import CardForeground from "@/components/CardElement-components/card-elements/CardForeground";
import CharacterBorder from "@/components/CardElement-components/card-elements/CharacterBorder";
import { Subscription } from "@/types";

export default function CardCharacter({
  subscription,
}: {
  subscription: Subscription;
}) {
  return (
    <>
      <CardBackground cardType={"character"} quality={25} />
      <CardBorder cardType={"character"} quality={25} absolute={true} />
      <CardForeground zIndex={4} />
      <CardWaterMark cardType={"character"} subscription={subscription} />
      <CharacterCardBorderBottom cardType={"character"} quality={25} />
      <CharacterBorder />
      <CardCounter />
      <CardAttribute cardKind={"character"} />
      <CardCost character={true} />
      <CardPrintWave
        quality={1}
        cardType={"character"}
        cardKindRoute={"character"}
      />
      <CardRarity
        quality={1}
        cardType={"character"}
        cardKindRoute={"character"}
      />
      <CardAbilityAndTrigger
        triggerQuality={1}
        cardType={"character"}
        client={true}
      />
      <CardName cardType={"character"} />
      <CardKind cardType={"character"} />
      <CardType cardType={"character"} />
      <CardPower cardType={"character"} />
      <CardCounterText />
      <CardSetAndNum cardType={"character"} />
      <CardRarityText cardType={"character"} client={true} />
      <CardPrintWaveText cardType={"character"} client={true} />
      <CardArtistText cardType={"character"} />
      <CardMadeWith cardType={"character"} />
      <CardColorWheel cardKind={"character"} />
    </>
  );
}
