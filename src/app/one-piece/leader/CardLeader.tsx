import CardBackground from "@/components/CardElement-components/card-elements/CardBackground";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";
import CardAttribute from "@/components/CardElement-components/card-elements/CardAttribute";
import CardCost from "@/components/CardElement-components/card-elements/CardCost";

import CardPrintWave from "@/components/CardElement-components/card-elements/CardPrintWave";
import CardRarity from "@/components/CardElement-components/card-elements/CardRarity";

import CardName from "@/components/CardElement-components/card-elements/CardName";
import CardKind from "@/components/CardElement-components/card-elements/CardKind";
import CardType from "@/components/CardElement-components/card-elements/CardType";
import CardPower from "@/components/CardElement-components/card-elements/CardPower";

import CardSetAndNum from "@/components/CardElement-components/card-elements/CardSetAndNum";
import CardRarityText from "@/components/CardElement-components/card-elements/CardRarityText";
import CardPrintWaveText from "@/components/CardElement-components/card-elements/CardPrintWaveText";
import CardArtistText from "@/components/CardElement-components/card-elements/CardArtistText";
import CardMadeWith from "@/components/CardElement-components/card-elements/CardMadeWith";

import LeaderCardBorderBottom from "@/components/CardElement-components/card-elements/LeaderCardBorderBottom";
import CardAbilityLeader from "@/components/CardElement-components/card-elements/CardAbilityLeader";

import React from "react";

import CardWaterMark from "@/components/CardElement-components/CardWaterMark";

import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";

import CardForeground from "@/components/CardElement-components/card-elements/CardForeground";
import LeaderBorder from "@/components/CardElement-components/card-elements/LeaderBorder";
import { Subscription } from "@/types";

export default function CardLeader({
  subscription,
}: {
  subscription: Subscription;
}) {
  return (
    <>
      <CardBackground cardType={"leader"} quality={25} />

      <CardBorder cardType={"leader"} quality={25} absolute={true} />

      <CardForeground />
      <CardWaterMark subscription={subscription} />
      <LeaderCardBorderBottom cardType={"leader"} quality={25} />
      <LeaderBorder />
      <CardAttribute cardKind={"leader"} />
      <CardCost leader={true} />
      <CardPrintWave quality={1} cardType={"leader"} cardKindRoute={"leader"} />
      <CardRarity quality={1} cardType={"leader"} cardKindRoute={"leader"} />
      <CardAbilityLeader client={true} />
      <CardName cardType={"leader"} />
      <CardKind cardType={"leader"} />
      <CardType cardType={"leader"} />
      <CardPower cardType={"leader"} />
      <CardSetAndNum cardType={"leader"} />
      <CardRarityText cardType={"leader"} client={true} />
      <CardPrintWaveText cardType={"leader"} client={true} />
      <CardArtistText cardType={"leader"} />
      <CardMadeWith cardType={"leader"} />
      <CardColorWheel cardKind={"leader"} />
    </>
  );
}
