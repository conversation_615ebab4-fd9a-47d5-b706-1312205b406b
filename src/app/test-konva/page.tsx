"use client";
import React from "react";
// import KonvaCanvasProvider from "@/components/konvajs/canvas-provider";
// import TestRichText from "@/components/konvajs/TestRichText";

export default function TestKonvaPage() {
  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-center text-3xl font-bold">
          Konva Rich Text Renderer Test
        </h1>

        <div className="mb-8 rounded-lg bg-white p-6 shadow-lg">
          {/*<TestRichText />*/}
        </div>

        <div className="rounded-lg bg-white p-6 shadow-lg">
          <h2 className="mb-4 text-xl font-semibold">
            Full Rich Text HTML Content on Canvas
          </h2>

          <div className="mb-4 rounded bg-gray-50 p-4">
            <h3 className="mb-2 font-medium">Original HTML:</h3>
            <code className="text-sm break-all text-gray-700">
              {`<p style="text-align: left">Hello, <span style="color: #2F77B3" class="blue-ability">OnPlay</span> <span style="color: #d94880" class="pink-ability">Once Per Turn</span> <span class="orange-ability-container"><span class="orange-ability-shadow"><span style="color: #DC8535" class="orange-ability">Blocker</span></span></span> <span style="color: #ba212f" class="red-ability">Counter</span> <span class="trigger-ability-container"><span class="trigger-ability-shadow"><span style="color: #f8ed70" class="trigger-ability">Trigger</span></span></span> <span class="black-ability-container"><span class="black-ability-shadow"><span style="color: #000000" class="black-ability">Don!! ×1</span></span></span><span style="color: rgb(255, 255, 255)" class=""> </span><span style="color: #FFFFFF" class="white-ability">1</span></p><ul><li><p>some ul with <span style="color: #d94880" class="pink-ability">styles</span></p></li></ul><p>then no ul</p>`}
            </code>
          </div>

          <div className="rounded-lg border bg-white p-4">
            <h3 className="mb-4 font-medium">Rendered on Konva Canvas:</h3>
            {/*<KonvaCanvasProvider />*/}
          </div>
        </div>
      </div>
    </div>
  );
}
