"use client";
import React, { useEffect, useRef } from "react";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
export default function Card({
  children,
  maxWRem = 0,
  maxWPx = 0,
  className = "",
}: {
  children: React.ReactNode;
  maxWRem?: number;
  maxWPx?: number;
  className?: string;
}) {
  const target = useRef<null | HTMLDivElement>(null);
  const observerRef = useRef<null | MutationObserver>(null);
  useEffect(function () {
    const targetNode = document.getElementById("card-element");
    const config = { childList: true, subtree: true };
    observerRef.current = new MutationObserver((mutationsList) => {
      for (const mutation of mutationsList) {
        if (mutation.type === "childList") {
          mutation.removedNodes.forEach((node) => {
            // @ts-expect-error working
            if (node?.id === "utcgcmwm") {
              const element = document.createElement("div");
              element.id = "utcgcmwm";
              document
                .getElementById("card-element")
                ?.insertAdjacentElement("afterbegin", element);
            }
          });
        }
      }
    });
    if (targetNode) {
      observerRef.current.observe(targetNode, config);
    }
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  });
  const fontsize = useGetResponsiveFontsizeInPx();
  const borderRadius = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: 33,
  });
  return (
    <div
      id={"card-element"}
      onContextMenu={(e) => {
        e.preventDefault();
      }}
      style={{
        fontSize: `${fontsize}px`,
        maxWidth: `${maxWRem ? maxWRem + "rem" : maxWPx ? maxWPx + "px" : ""}`,
        borderRadius: `${borderRadius}px`,
      }}
      ref={target}
      className={`relative ${className} font-roboto ${maxWRem ? "3xl:max-w-[32rem]!" : ""} sticky top-20 h-full overflow-clip text-white`}
    >
      {children}
    </div>
  );
}
