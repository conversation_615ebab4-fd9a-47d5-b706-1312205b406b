import { Suspense } from "react";
import { Subscription } from "@/types";

export default function CardWaterMark({
  cardType = "",
  subscription,
}: {
  cardType?: "character" | "leader" | "event" | "stage" | "don" | "";
  subscription: Subscription;
}) {
  return (
    <Suspense fallback={null}>
      <WaterMark cardType={cardType} subscription={subscription} />
    </Suspense>
  );
}

async function WaterMark({
  cardType = "",
  subscription,
}: {
  cardType?: "character" | "leader" | "event" | "stage" | "don" | "";
  subscription: Subscription;
}) {
  // const subscription = await getSubscriptionNameAndStatus();

  if (!subscription.active)
    return (
      <div
        id={"utcgcmwm"}
        className={`${cardType === "character" ? "z-4" : "z-0"}`}
      />
    );
  return null;
}
