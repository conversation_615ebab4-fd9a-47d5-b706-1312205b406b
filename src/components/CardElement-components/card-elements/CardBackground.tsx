"use client";
import Image from "next/image";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getCardType } from "@/helpers/getCardType";

import { LeaderBorder } from "@/types";
import { getCardColor } from "@/app/helpers/getCardColor";
import { generateMultiColorGradient } from "@/app/helpers/generateMultiColorGradient";
const cardBackground = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Card-Background.png`;
const printReadyCardBackground = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Card-Background.png`;
const optimizedCardBackground = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Card-Background.webp`;
const optimizedPrintReadyCardBackground = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Card-Background.webp`;
import React from "react";
export default function CardBackground({
  cardType,
  quality,
  png = false,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  quality: number;
  png?: boolean;
}) {
  const backgroundImageUrl = useGetStoreState("backgroundImageUrl") as string;
  const cardTypeRoute = getCardType(cardType);
  const printReady = useGetStoreState("printReady");
  const eventBorder = useGetStoreState("eventBorder");
  const colorArray = useGetStoreState("colorArray");
  const color =
    Array.isArray(colorArray) && colorArray.length > 0 ? colorArray[0] : "red";

  // Get background colors for the first and last colors
  const backgroundColor = getCardColor(color, false, false, false, false, true);

  // Generate multi-color gradient based on the number of colors
  const multiColorGradient = Array.isArray(colorArray)
    ? generateMultiColorGradient(colorArray, true)
    : `linear-gradient(to right, ${backgroundColor}, ${backgroundColor})`;
  const leaderBorder = useGetStoreState("leaderBorder") as LeaderBorder;
  if (backgroundImageUrl) {
    return (
      <img
        className={"absolute h-full w-full object-cover"}
        src={backgroundImageUrl}
        alt={"your image"}
      />
    );
  }
  return (
    <>
      {/*  {cardType !== "don" && (
        <Image
          width={3357}
          height={4692}
          className={"absolute z-[-2]"}
          priority
          loading={"eager"}
          title={`${cardTypeRoute} ${color} card background`}
          src={
            png
              ? `/assets/${cardTypeRoute}/${color}Background.png`
              : `/assets/${cardTypeRoute}/optimized/${color}Background.webp`
          }
          alt={`${cardTypeRoute} ${color} card background`}
          quality={quality}
          sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
        />
      )}*/}
      {(cardType === "character" ||
        (cardType === "event" && eventBorder !== "eb-02") ||
        cardType === "stage") && (
        <>
          <div
            className={"absolute z-[-3] h-full w-full brightness-[65%]"}
            style={{
              background:
                Array.isArray(colorArray) && colorArray.length > 1
                  ? multiColorGradient
                  : backgroundColor,
            }}
          ></div>
        </>
      )}{" "}
      {cardType === "event" && eventBorder === "eb-02" && (
        <>
          <div
            className={"absolute z-[-3] h-full w-full"}
            style={{
              background: `linear-gradient(to right,#FAF7EF ,#FAF7EF)`,
            }}
          ></div>
        </>
      )}
      {cardType === "leader" &&
        leaderBorder !== "standard-white" &&
        leaderBorder !== "AA-black-and-white" && (
          <>
            <div
              className={"absolute z-[-3] h-full w-full brightness-[65%]"}
              style={{
                background:
                  Array.isArray(colorArray) && colorArray.length > 1
                    ? multiColorGradient
                    : backgroundColor,
              }}
            ></div>
          </>
        )}
      {cardType === "leader" &&
        (leaderBorder === "standard-white" ||
          leaderBorder === "AA-black-and-white") && (
          <>
            <div
              className={"absolute z-[-3] h-full w-full brightness-[100%]"}
              style={{
                background: `linear-gradient(to right,#fff 50%,#fff 50%)`,
              }}
            ></div>
          </>
        )}
      {cardType === "don" && (
        <>
          <div
            className={"absolute z-[-3] h-full w-full brightness-[65%]"}
            style={{ backgroundColor: "#272424" }}
          ></div>
        </>
      )}
      {(cardType === "character" ||
        (cardType === "event" && eventBorder !== "eb-02") ||
        cardType === "stage" ||
        cardType === "don") && (
        <div
          className={"background-gradient opacity-[40%]"}
          style={{
            maskImage: `url(${png ? (printReady ? printReadyCardBackground : cardBackground) : printReady ? optimizedPrintReadyCardBackground : optimizedCardBackground})`,
          }}
        ></div>
      )}
      {cardType === "leader" &&
        leaderBorder !== "standard-white" &&
        leaderBorder !== "AA-black-and-white" &&
        leaderBorder !== "AA-white" && (
          <div
            className={"background-gradient opacity-[40%]"}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyCardBackground : cardBackground) : printReady ? optimizedPrintReadyCardBackground : optimizedCardBackground})`,
            }}
          ></div>
        )}
      {cardType === "leader" && leaderBorder === "AA-white" && (
        <div
          className={"background-gradient-white opacity-[90%]"}
          style={{
            maskImage: `url(${png ? (printReady ? printReadyCardBackground : cardBackground) : printReady ? optimizedPrintReadyCardBackground : optimizedCardBackground})`,
          }}
        ></div>
      )}
      {cardType === "leader" && leaderBorder === "standard-white" && (
        <Image
          width={printReady ? 3677 : 3357}
          height={printReady ? 5011 : 4692}
          priority
          loading={"eager"}
          title={`${cardTypeRoute} card border`}
          className={`absolute top-0 max-w-full`}
          src={
            png
              ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Black-White-Pattern.png`
              : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Black-White-Pattern.webp`
          }
          alt={`${cardTypeRoute} card border`}
          quality={quality}
          sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
        />
      )}
      {/*{cardType === "don" && (
        <Image
          width={3357}
          height={4692}
          className={"absolute z-[-2]"}
          src={
            png
              ? `/assets/${cardTypeRoute}/blackBackground.png`
              : `/assets/${cardTypeRoute}/optimized/blackBackground.webp`
          }
          alt={`${cardTypeRoute} ${color} card border`}
          quality={quality}
          sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
        />
      )}*/}
    </>
  );
}
