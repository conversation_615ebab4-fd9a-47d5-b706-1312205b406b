"use client";
import Image from "next/image";

import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getCardType } from "@/helpers/getCardType";

const SPL1 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Character-SP-V2-L1.png`;
const optimizedSPL1 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Character-SP-V2-L1.webp`;
const printReadySPL1 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Character-SP-V2-L1.png`;
const printReadyOptimizedSPL1 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Character-SP-V2-L1.webp`;
const SPL3 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Character-SP-V2-L3.png`;
const optimizedSPL3 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Character-SP-V2-L3.webp`;
const printReadySPL3 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Character-SP-V2-L3.png`;
const printReadyOptimizedSPL3 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Character-SP-V2-L3.webp`;
const border = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Card-Border.png`;
const optimizedBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Card-Border.webp`;
const printReadyBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Card-Border.png`;
const optimizedPrintReadyBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Card-Border.webp`;
const borderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Card-Border-Event-Stage-Don.png`;
const optimizedBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Card-Border-Event-Stage-Don.webp`;
const printReadyBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Card-Border-Event-Stage-Don.png`;
const optimizedPrintReadyBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Card-Border-Event-Stage-Don.webp`;
const borderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Card-Border-Event-EB02-New.png`;
const optimizedBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Card-Border-Event-EB02-New.webp`;
const printReadyBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Card-Border-Event-EB02-New.png`;
const optimizedPrintReadyBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Card-Border-Event-EB02-New.webp`;
const srPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Event-Stage-Character_SR-Border-Pattern.png`;
const optimizedSrPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Event-Stage-Character_SR-Border-Pattern.webp`;
const printReadySrPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Event-Stage-Character_SR-Border-Pattern.png`;
const optimizedPrintReadySrPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Event-Stage-Character_SR-Border-Pattern.webp`;
const eventStagePattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Event-Stage-Character_SR-Border-Pattern-New.png`;
const optimizedEventStagePattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Event-Stage-Character_SR-Border-Pattern-New.webp`;
const printReadyEventStagePattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Event-Stage-Character_SR-Border-Pattern-New.png`;
const optimizedPrintReadyEventStagePattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Event-Stage-Character_SR-Border-Pattern-New.webp`;
const secPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/SEC-Border-Pattern.png`;
const optimizedSecPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/SEC-Border-Pattern.webp`;
const printReadySecPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-SEC-Border-Pattern.png`;
const optimizedPrintReadySecPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-SEC-Border-Pattern.webp`;
const innerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Character-Inner-Border.png`;
const optimizedInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Character-Inner-Border.webp`;
const printReadyInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Character-Inner-Border.png`;
const optimizedPrintReadyInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Character-Inner-Border.webp`;
const innerBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Event_Stage_Don-Inner-Border.png`;
const optimizedInnerBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Event_Stage_Don-Inner-Border.webp`;
const printReadyInnerBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Event_Stage_Don-Inner-Border.png`;
const optimizedPrintReadyInnerBorderEventStage = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Event_Stage_Don-Inner-Border.webp`;
const innerBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Card-Frame-Event-EB02-No-Wheel-Border.png`;
const optimizedInnerBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Card-Frame-Event-EB02-No-Wheel-Border.webp`;
const printReadyInnerBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Card-Frame-Event-EB02-No-Wheel-Border.png`;
const optimizedPrintReadyInnerBorderEventEB02 = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Card-Frame-Event-EB02-No-Wheel-Border.webp`;
const leaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Leader-Inner-Border.png`;
const optimizedLeaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Leader-Inner-Border.webp`;
const printReadyLeaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Leader-Inner-Border.png`;
const optimizedPrintReadyLeaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Leader-Inner-Border.webp`;
const leaderInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Leader-Inner-Border-Top-v2.webp`;
const optimizedLeaderInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Leader-Inner-Border-Top.webp`;
const printReadyLeaderInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Leader-Inner-Border-Top-v2.png`;
const optimizedPrintReadyLeaderInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Leader-Inner-Border-Top-v2.webp`;
const characterInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Character-Inner-Border-Top.png`;
const optimizedCharacterInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Leader-Inner-Border-Top.webp`;
const printReadyCharacterInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Character-Inner-Border-Top.png`;
const optimizedPrintReadyCharacterInnerBorderTop = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Character-Inner-Border-Top.webp`;
const leaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Leader-Border-Pattern-New.png`;
const optimizedLeaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Leader-Border-Pattern-New.webp`;
const printReadyLeaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Leader-Border-Pattern-New.png`;
const optimizedPrintReadyLeaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Leader-Border-Pattern-New.webp`;
const eventStageAbility = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Event_Stage-Ability.png`;
const optimizedEventStageAbility = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Event_Stage-Ability.webp`;
const printReadyEventStageAbility = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Event_Stage-Ability.png`;
const optimizedPrintReadyEventStageAbility = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Event_Stage-Ability.webp`;
const printReadyInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Inner-Border-Bottom-Shadow-v2.png`;
const innerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Inner-Border-Bottom-Shadow-v2.png`;
const printReadyOptimizedInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Inner-Border-Bottom-Shadow-v2.webp`;
const optimizedInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Inner-Border-Bottom-Shadow-v2.webp`;
import { getCardColor } from "@/app/helpers/getCardColor";
import getLeaderBorderGradient from "@/app/helpers/getLeaderBorderGradient";
import getMultiColorBorderGradient from "@/app/helpers/getMultiColorBorderGradient";
import { generatePatternGradient } from "@/app/helpers/generatePatternGradient";
import { generateMultiColorGradient } from "@/app/helpers/generateMultiColorGradient";

export default function CardBorder({
  absolute = false,
  cardType,
  quality,
  png = false,
  className = "",
  zIndex = 0,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  quality: number;
  png?: boolean;
  absolute?: boolean;
  className?: string;
  zIndex?: number;
}) {
  const printReady = useGetStoreState("printReady");
  const cardTypeRoute = getCardType(cardType);
  const abilityBackground = useGetStoreState("abilityBackground");
  const characterBorder = useGetStoreState("characterBorder");

  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const color2 = colorArray[colorArray.length - 1];
  const foilBorder = useGetStoreState("foilBorder");
  const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");
  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const cardColor = getCardColor(color);
  /*  let leaderBorderRoute;
  if (leaderBorder === "standard" || leaderBorder === "black") {
    leaderBorderRoute = "Color-Leader";
  }*/

  // Use the multi-color border gradient function if we have more than 2 colors
  let [backgroundGradient, backgroundBlendMode, patternGradient] =
    (Array.isArray(colorArray) &&
      colorArray.length > 2 &&
      cardType !== "leader") ||
    (Array.isArray(colorArray) &&
      colorArray.length > 2 &&
      cardType === "leader")
      ? getMultiColorBorderGradient(colorArray, leaderBorder, cardType)
      : getLeaderBorderGradient(color, color2, leaderBorder, cardType);

  // For multiple colors, set the appropriate blend mode
  if (
    (Array.isArray(colorArray) &&
      colorArray.length >= 2 &&
      color !== color2 &&
      cardType !== "leader") ||
    (Array.isArray(colorArray) &&
      colorArray.length >= 3 &&
      color !== color2 &&
      cardType === "leader")
  ) {
    // Use screen for 2 colors, and soft-light for 3+ colors for better blending with our overlapping gradients
    backgroundBlendMode = colorArray.length === 2 ? "screen" : "soft-light";
  }

  // Generate a custom pattern gradient for multiple colors
  if (Array.isArray(colorArray) && colorArray.length > 2) {
    patternGradient = generatePatternGradient(colorArray);
  }
  if (cardType === "character" && characterBorder === "sec") {
    backgroundGradient = "linear-gradient(to right,#D5B842 ,#D5B842)";
  }
  if (cardType === "character" && characterBorder === "sr") {
    backgroundGradient = "linear-gradient(to right,#DAE1E8 ,#DAE1E8)";
  }
  if (cardType === "character" && characterBorder === "standard") {
    backgroundGradient = "linear-gradient(to right,#fff ,#fff)";
  }
  if (cardType === "event") {
    backgroundGradient = "linear-gradient(to right,#D5B563 ,#D5B563)";
    patternGradient = "linear-gradient(to right,#A18D51  , #A18D51 )";
    if (foilBorder) {
      backgroundGradient = "linear-gradient(to right,#A18D51  , #A18D51 )";
      patternGradient = "linear-gradient(to right,#E5D181 ,#E5D181)";
    }
  }

  if (cardType === "stage") {
    backgroundGradient = "linear-gradient(to right,#152A54 ,#152A54)";
    patternGradient = "linear-gradient(to right,#4969AA  , #4969AA )";
  }
  if (cardType === "don") {
    backgroundGradient = "linear-gradient(to right,#000 ,#000)";
  }
  if (cardType === "leader" && leaderBorder === "full-art") {
    return (
      <Image
        width={printReady ? 3677 : 3357}
        height={printReady ? 5011 : 4692}
        priority
        loading={"eager"}
        title={`${cardTypeRoute} ${color} card border`}
        className={`${
          absolute ? "absolute" : "relative"
        } top-0 max-w-full opacity-0`}
        src={
          png
            ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Leader-Inner-Border-Bottom.png`
            : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Leader-Inner-Border-Bottom.webp`
        }
        alt={`${cardTypeRoute} ${color} card border`}
        quality={quality}
        sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
      />
    );
  }
  return (
    <>
      {/*The image bellow is important for cropper, without it the cropper will collapse*/}
      {cardType !== "don" && (
        <Image
          width={printReady ? 3677 : 3357}
          height={printReady ? 5011 : 4692}
          priority
          loading={"eager"}
          title={`${cardTypeRoute} ${color} card border`}
          className={`${
            absolute ? "absolute" : "relative"
          } top-0 max-w-full opacity-0`}
          src={
            png
              ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Leader-Inner-Border-Bottom.png`
              : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Leader-Inner-Border-Bottom.webp`
          }
          alt={`${cardTypeRoute} ${color} card border`}
          quality={quality}
          sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
        />
      )}
      {cardType === "character" && (
        <>
          {/*Border around the card for standard, sr, and sec */}
          {(characterBorder === "standard" ||
            characterBorder === "sr" ||
            characterBorder === "sec") && (
            <div
              className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyBorder : border) : printReady ? optimizedPrintReadyBorder : optimizedBorder})`,
                background: backgroundGradient,
                zIndex: zIndex,
              }}
            ></div>
          )}
          {/*Border pattern around the card for sr and sec */}
          {(characterBorder === "sr" || characterBorder === "sec") && (
            <div
              className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
              style={{
                maskImage: `url(${characterBorder === "sr" ? (png ? (printReady ? printReadySrPattern : srPattern) : printReady ? optimizedPrintReadySrPattern : optimizedSrPattern) : png ? (printReady ? printReadySecPattern : secPattern) : printReady ? optimizedPrintReadySecPattern : optimizedSecPattern})`,
                background: `linear-gradient(to right,${characterBorder === "sr" ? "#fff" : "#F6EF9D"} ,${characterBorder === "sr" ? "#fff" : "#F6EF9D"})`,
                zIndex: zIndex,
              }}
            ></div>
          )}

          {/*<div*/}
          {/*  className={`border-gradient-shadow top-0 ${absolute ? "absolute" : "relative"} ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : "opacity-[15%]"} */}
          {/*`}*/}
          {/*  style={{*/}
          {/*    maskImage: `url(${png ? innerBorder : optimizedInnerBorder})`,*/}
          {/*    background: `linear-gradient(to right,#000 ,#000)`,*/}
          {/*    zIndex: zIndex,*/}
          {/*  }}*/}
          {/*></div>*/}

          {leaderBorderEnabled && (
            // Character Inner border shadow outline
            <>
              <div
                className={`border-gradient-shadow top-0 ${className} ${absolute ? "absolute" : "relative"} ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : "opacity-[7.5%]"} `}
                style={{
                  maskImage: `url(${png ? (printReady ? printReadyInnerBorder : innerBorder) : printReady ? optimizedPrintReadyInnerBorder : optimizedInnerBorder})`,
                  background: `linear-gradient(to right,#000 ,#000)`,
                  zIndex: zIndex,
                }}
              ></div>
            </>
          )}
          {!leaderBorderEnabled && (
            <>
              {/* Character Inner border shadow outline top*/}
              <div
                className={`border-gradient-shadow top-0 ${className} ${absolute ? "absolute" : "relative"} ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : "opacity-[15%]"} `}
                style={{
                  maskImage: `url(${png ? (printReady ? printReadyCharacterInnerBorderTop : characterInnerBorderTop) : printReady ? optimizedPrintReadyCharacterInnerBorderTop : optimizedCharacterInnerBorderTop})`,
                  background: `linear-gradient(to right,#000 ,#000)`,
                  zIndex: zIndex,
                }}
              ></div>
              {/* Character Inner border shadow outline bottom*/}
              <div
                className={`border-gradient-shadow top-0 ${className} ${absolute ? "absolute" : "relative"} ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : "opacity-[15%]"} `}
                style={{
                  maskImage: `url(${png ? (printReady ? printReadyInnerBorderBottomShadow : innerBorderBottomShadow) : printReady ? printReadyOptimizedInnerBorderBottomShadow : optimizedInnerBorderBottomShadow})`,
                  background: `linear-gradient(to right,#000 ,#000)`,
                  zIndex: zIndex,
                }}
              ></div>
            </>
          )}
          {/*Character Inner border*/}
          <div
            className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} ${(characterBorder === "none" || characterBorder === "sp-v2") && cardType === "character" ? "opacity-0" : ""} `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyInnerBorder : innerBorder) : printReady ? optimizedPrintReadyInnerBorder : optimizedInnerBorder})`,
              background: Array.isArray(colorArray)
                ? generateMultiColorGradient(colorArray)
                : `linear-gradient(to right,${cardColor},  ${cardColor})`,
              backgroundBlendMode:
                Array.isArray(colorArray) && colorArray.length > 2
                  ? "soft-light"
                  : Array.isArray(colorArray) &&
                      colorArray.length === 2 &&
                      color !== color2
                    ? "screen"
                    : "",
              zIndex: zIndex,
            }}
          ></div>
        </>
      )}

      {cardType === "leader" && (
        <>
          <div
            className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} ${
              leaderBorder === "standard" ||
              leaderBorder === "AA-white" ||
              leaderBorder === "AA-black-and-white" ||
              leaderBorder === "standard-white"
                ? "opacity-100"
                : "opacity-0"
            } `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyBorder : border) : printReady ? optimizedPrintReadyBorder : optimizedBorder})`,
              background: backgroundGradient,
              backgroundBlendMode: backgroundBlendMode,
              zIndex: zIndex,
            }}
          ></div>
          {leaderBorder === "standard-white" && (
            <Image
              width={printReady ? 3677 : 3357}
              height={printReady ? 5011 : 4692}
              priority
              loading={"eager"}
              title={`${cardTypeRoute} card border`}
              className={`absolute top-0 max-w-full ${className}`}
              src={
                png
                  ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Black-White-Border-Pattern.png`
                  : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Black-White-Border-Pattern.webp`
              }
              alt={`${cardTypeRoute} card border`}
              quality={quality}
              style={{ zIndex: zIndex }}
              sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
            />
          )}

          {leaderBorder === "rainbow" && (
            <div
              className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyBorder : border) : printReady ? optimizedPrintReadyBorder : optimizedBorder})`,
                background: backgroundGradient,
                backgroundBlendMode: backgroundBlendMode,
                zIndex: zIndex,
              }}
            ></div>
          )}

          {/*          <div
            className={`border-gradient-shadow top-0 ${absolute ? "absolute" : "relative"} opacity-[15%]
          `}
            style={{
              maskImage: `url(${png ? leaderInnerBorder : optimizedLeaderInnerBorder})`,
              background: `linear-gradient(to right,#000 ,#000)`,
              zIndex: zIndex,
            }}
          ></div>*/}
          {leaderBorderEnabled && (
            <div
              className={`border-gradient-shadow top-0 ${className} ${absolute ? "absolute" : "relative"} opacity-[15%]`}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyLeaderInnerBorder : leaderInnerBorder) : printReady ? optimizedPrintReadyLeaderInnerBorder : optimizedLeaderInnerBorder})`,
                background: `linear-gradient(to right,#000 ,#000)`,
                zIndex: zIndex,
              }}
            ></div>
          )}
          {!leaderBorderEnabled && (
            <div
              className={`border-gradient-shadow top-0 ${className} ${absolute ? "absolute" : "relative"} opacity-[15%]`}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyLeaderInnerBorderTop : leaderInnerBorderTop) : printReady ? optimizedPrintReadyLeaderInnerBorderTop : optimizedLeaderInnerBorderTop})`,
                background: `linear-gradient(to right,#000 ,#000)`,
                zIndex: zIndex,
              }}
            ></div>
          )}

          {(leaderBorder === "25th" ||
            leaderBorder === "standard" ||
            leaderBorder === "rainbow") && (
            <div
              className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyLeaderInnerBorder : leaderInnerBorder) : printReady ? optimizedPrintReadyLeaderInnerBorder : optimizedLeaderInnerBorder})`,
                background: `linear-gradient(to right, #080809,  #080809)`,
                zIndex: zIndex,
              }}
            ></div>
          )}

          <>
            <div
              className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyLeaderInnerBorder : leaderInnerBorder) : printReady ? optimizedPrintReadyLeaderInnerBorder : optimizedLeaderInnerBorder})`,
                background: `linear-gradient(to right, ${leaderBorder === "AA-white" ? "#fff" : "#080809"},  ${leaderBorder === "AA-white" ? "#fff" : "#080809"})`,
                zIndex: zIndex,
              }}
            ></div>
            <div
              className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} ${leaderBorder === "AA-white" ? "opacity-70" : leaderBorder === "AA-black" || leaderBorder === "AA-black-and-white" ? "opacity-50" : "opacity-0"} `}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyLeaderBorderPattern : leaderBorderPattern) : printReady ? optimizedPrintReadyLeaderBorderPattern : optimizedLeaderBorderPattern})`,
                background: patternGradient,
                backgroundBlendMode: backgroundBlendMode,
                zIndex: zIndex,
              }}
            ></div>
          </>
        </>
      )}

      {((cardType === "event" && eventBorder === "standard") ||
        cardType === "stage") && (
        <>
          {/* Card border around the card for event and stage */}
          <div
            className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyBorderEventStage : borderEventStage) : printReady ? optimizedPrintReadyBorderEventStage : optimizedBorderEventStage})`,
              background: backgroundGradient,
              zIndex: zIndex,
            }}
          ></div>
          {abilityBackground && (
            <div
              className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyEventStageAbility : eventStageAbility) : printReady ? optimizedPrintReadyEventStageAbility : optimizedEventStageAbility})`,
                background: `linear-gradient(to right, #F2F2DC , #F2F2DC)`,
                zIndex: zIndex,
              }}
            ></div>
          )}
          {/* Card border pattern for event and stage*/}
          <div
            className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyEventStagePattern : eventStagePattern) : printReady ? optimizedPrintReadyEventStagePattern : optimizedEventStagePattern})`,
              background: patternGradient,
              zIndex: zIndex,
            }}
          ></div>

          {/* Inner border shadow for event and stage */}
          <div
            className={`border-gradient-shadow top-0 ${className} ${absolute ? "absolute" : "relative"} opacity-[15%]`}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyInnerBorderEventStage : innerBorderEventStage) : printReady ? optimizedPrintReadyInnerBorderEventStage : optimizedInnerBorderEventStage})`,
              background: `linear-gradient(to right,#000 ,#000)`,
              zIndex: zIndex,
            }}
          ></div>
          {/* Inner border for event and stage */}

          <div
            className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyInnerBorderEventStage : innerBorderEventStage) : printReady ? optimizedPrintReadyInnerBorderEventStage : optimizedInnerBorderEventStage})`,
              background: Array.isArray(colorArray)
                ? generateMultiColorGradient(colorArray)
                : `linear-gradient(to right,${cardColor},  ${cardColor})`,
              backgroundBlendMode:
                Array.isArray(colorArray) && colorArray.length > 2
                  ? "soft-light"
                  : Array.isArray(colorArray) &&
                      colorArray.length === 2 &&
                      color !== color2
                    ? "screen"
                    : "",
              zIndex: zIndex,
            }}
          ></div>
        </>
      )}
      {eventBorder === "op-10" && cardType === "event" && (
        <>
          {/*SP V2 Card name background 1 (dimmer version)*/}
          <div
            className={`border-bottom-gradient top-0 ${absolute ? "absolute" : "relative"} brightness-50`}
            style={{
              maskImage: `url(${png ? (printReady ? printReadySPL1 : SPL1) : printReady ? printReadyOptimizedSPL1 : optimizedSPL1})`,
              background: `linear-gradient(to right, #160B0D, #160B0D)`,
              backgroundBlendMode:
                Array.isArray(colorArray) && colorArray.length > 2
                  ? "soft-light"
                  : Array.isArray(colorArray) &&
                      colorArray.length === 2 &&
                      colorArray[0] !== colorArray[colorArray.length - 1]
                    ? "screen"
                    : "",
              zIndex: 4,
            }}
          ></div>
          <div
            className={`border-bottom-gradient top-0 ${absolute ? "absolute" : "relative"} brightness-50`}
            style={{
              maskImage: `url(${
                png
                  ? printReady
                    ? printReadySPL3
                    : SPL3
                  : printReady
                    ? printReadyOptimizedSPL3
                    : optimizedSPL3
              })`,
              background: `linear-gradient(to right, #ff0303, #ff0303)`,
              backgroundBlendMode:
                Array.isArray(colorArray) && colorArray.length > 2
                  ? "soft-light"
                  : Array.isArray(colorArray) &&
                      colorArray.length === 2 &&
                      colorArray[0] !== colorArray[colorArray.length - 1]
                    ? "screen"
                    : "",
              zIndex: 4,
            }}
          ></div>
        </>
      )}
      {cardType === "event" && eventBorder === "eb-02" && (
        <>
          <Image
            alt={"Event Border EB-02"}
            src={`${png ? (printReady ? printReadyBorderEventEB02 : borderEventEB02) : printReady ? optimizedPrintReadyBorderEventEB02 : optimizedBorderEventEB02}`}
            height={5011}
            width={3677}
            priority
            className={`absolute top-0 max-w-full ${className}`}
            style={{ zIndex: zIndex }}
          />

          {/* Inner border for event and stage */}

          <div
            className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyInnerBorderEventEB02 : innerBorderEventEB02) : printReady ? optimizedPrintReadyInnerBorderEventEB02 : optimizedInnerBorderEventEB02})`,
              background: Array.isArray(colorArray)
                ? generateMultiColorGradient(colorArray, false, {
                    border: cardType === "event" && eventBorder === "eb-02",
                    other: false,
                  })
                : `linear-gradient(to right,${cardColor},  ${cardColor})`,
              backgroundBlendMode:
                Array.isArray(colorArray) && colorArray.length > 2
                  ? "soft-light"
                  : Array.isArray(colorArray) &&
                      colorArray.length === 2 &&
                      color !== color2
                    ? "screen"
                    : "",
              zIndex: zIndex,
            }}
          ></div>
        </>
      )}

      {cardType === "don" && (
        <>
          {/* Card border around the card for event and stage */}
          <div
            className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyBorderEventStage : borderEventStage) : printReady ? optimizedPrintReadyBorderEventStage : optimizedBorderEventStage})`,
              background: backgroundGradient,
              zIndex: zIndex,
            }}
          ></div>

          {/* Inner border shadow for event and stage */}
          <div
            className={`border-gradient-shadow top-0 ${className} ${absolute ? "absolute" : "relative"} opacity-[8%]`}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyInnerBorderEventStage : innerBorderEventStage) : printReady ? optimizedPrintReadyInnerBorderEventStage : optimizedInnerBorderEventStage})`,
              background: `linear-gradient(to bottom,#000 ,#fff)`,
              zIndex: zIndex,
            }}
          ></div>
          {/* Inner border for event and stage */}

          <div
            className={`border-bottom-gradient top-0 ${className} ${absolute ? "absolute" : "relative"} `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyInnerBorderEventStage : innerBorderEventStage) : printReady ? optimizedPrintReadyInnerBorderEventStage : optimizedInnerBorderEventStage})`,
              background: "linear-gradient(to right,#000 ,#000)",
              zIndex: zIndex,
            }}
          ></div>
          <Image
            width={printReady && png ? 3677 : 3357}
            height={printReady && png ? 5011 : 4692}
            priority
            loading={"eager"}
            title={`Don card border`}
            className={`relative top-0 max-w-full opacity-0`}
            src={`${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Leader-Inner-Border-Bottom.webp`}
            alt={`card border`}
            quality={0}
            sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
          />
        </>
      )}
    </>
  );
}
