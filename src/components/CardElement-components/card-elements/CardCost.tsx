"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useGetResponsiveFontsizeInPx } from "@/hooks/useGetResponsiveFontsizeInPx";
import { getCardColor } from "@/app/helpers/getCardColor";
import { generateCostGradient } from "@/app/helpers/generateCostGradient";
import { getPrintReadyDistanceString } from "@/app/helpers/getPrintReadyDistance";
import {
  getPrintReadySizeNumber,
  getPrintReadySizeString,
} from "@/app/helpers/getPrintReadySize";
import { CHARACTER_COLORS_SEC } from "@/components/CardElement-components/card-elements/characterBorderTypes";
const eventCostBackground = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/EB02-Cost-Background.png`;
const eventCostFrame = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/EB02-Cost-Frame.png`;
export default function CardCost({
  leader = false,

  character = false,
  cropper = false,
  eventOrStage = false,
  event = false,
}: {
  leader?: boolean;
  character?: boolean;
  cropper?: boolean;
  eventOrStage?: boolean;
  event?: boolean;
}) {
  const printReady = useGetStoreState("printReady");
  const characterCost = useGetStoreState("cost");
  const life = useGetStoreState("life");
  const characterBorder = useGetStoreState("characterBorder");
  const colorArray = useGetStoreState("colorArray");
  const color =
    Array.isArray(colorArray) && colorArray.length > 0 ? colorArray[0] : "red";

  const leaderBorder = useGetStoreState("leaderBorder");
  const eventBorder = useGetStoreState("eventBorder");
  const costOffsetStandard = getCharacterOffsetStyles(
    leader ? life : characterCost,
  );
  const cost = leader ? life : characterCost;
  const padding = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: 20.1,
  });

  // Generate cost gradient based on the number of colors
  const isRainbow = leaderBorder === "rainbow";
  const shadowColor = getCardColor(
    color,
    false,
    false,
    false,
    false,
    false,
    false,
    undefined,
    { border: false, other: eventBorder === "eb-02" && event },
  );
  const costGradient = Array.isArray(colorArray)
    ? generateCostGradient(colorArray, isRainbow, {
        border: false,
        other: eventBorder === "eb-02" && event,
      })
    : `linear-gradient(135deg, ${shadowColor}, ${shadowColor})`;

  let fontsizeNum = 62;
  const eventFontsizeNum = 81.5;
  let eventMarginLeft = "3.2%";

  const letterSpacing11 = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: 4.5,
  });
  const letterSpacing12 = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: -3,
  });
  let letterSpacing = undefined;
  if (cost === "11") {
    letterSpacing = letterSpacing11;
  }
  if (cost === "12") {
    letterSpacing = letterSpacing12;
  }
  if (!leader) {
    if (
      cost === "0" ||
      cost === "1" ||
      cost === "3" ||
      cost === "5" ||
      cost === "6" ||
      cost === "8" ||
      cost === "9" ||
      cost === "10" ||
      cost === "11" ||
      cost === "12" ||
      cost === "13" ||
      cost === "14" ||
      cost === "15" ||
      cost === "16" ||
      cost === "17" ||
      cost === "18" ||
      cost === "19"
    ) {
      fontsizeNum = 65;
    }
    if (cost === "4" || cost === "7") {
      fontsizeNum = 64;
    }
  }
  let leaderStyles = {
    right: "17.35%",
    bottom: "16.28%",
  };

  if (leader) {
    if (
      cost === "0" ||
      cost === "1" ||
      cost === "2" ||
      cost === "3" ||
      cost === "5" ||
      cost === "6" ||
      cost === "8" ||
      cost === "9" ||
      cost === "10"
    ) {
      fontsizeNum = 41.3;
    }
    if (cost === "4" || cost === "7") {
      fontsizeNum = 40.3;
      leaderStyles = {
        right: "17.45%",
        bottom: "16.28%",
      };
    }
    if (cost === "6") {
      leaderStyles = {
        right: "17.25%",
        bottom: "16.34%",
      };
    }
    if (cost === "5") {
      leaderStyles = {
        right: "17.30%",
        bottom: "16.28%",
      };
    }
    if (cost === "0") {
      leaderStyles = {
        right: "17.15%",
        bottom: "16.38%",
      };
    }
    if (cost === "1") {
      leaderStyles = {
        right: "16.85%",
        bottom: "16.38%",
      };
    }
    if (cost === "2") {
      leaderStyles = {
        right: "17.25%",
        bottom: "16.44%",
      };
    }
    if (cost === "3") {
      leaderStyles = {
        right: "17.13%",
        bottom: "16.38%",
      };
    }
    if (cost === "7") {
      leaderStyles = {
        right: "17.0%",
        bottom: "16.28%",
      };
    }
    if (cost === "8" || cost === "9") {
      leaderStyles = {
        right: "17.15%",
        bottom: "16.38%",
      };
    }
    if (Number(cost) >= 10) {
      leaderStyles = {
        right: "18.45%",
        bottom: "16.38%",
      };
    }
  }
  const fontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: printReady
      ? fontsizeNum - (leader ? 3.49 : 5.6)
      : fontsizeNum,
  });
  const eventFontsize = useGetResponsiveFontsizeInPx({
    desiredTextSizeInPx: printReady ? eventFontsizeNum - 5.6 : eventFontsizeNum,
  });

  let backgroundImage = "";

  if ((character || eventOrStage) && colorArray.length > 1) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }
  if (
    character &&
    (characterBorder === "sec" ||
      characterBorder === "sec-aa" ||
      characterBorder === "mr") &&
    color === "yellow"
  ) {
    backgroundImage = `linear-gradient(to right, #000,  #000)`;
  }

  let costColor;
  let costStyles = costGradient;
  const colors = Array.isArray(colorArray) ? colorArray : [color];

  if (leader) {
    if (colors.includes("yellow")) {
      costColor = "#fff";
      backgroundImage = `linear-gradient(to right, #000,  #000)`;
      if (
        colors.length === 1 &&
        leaderBorder !== "AA-black" &&
        leaderBorder !== "AA-black-and-white" &&
        leaderBorder !== "rainbow"
      ) {
        backgroundImage = ``;
        costColor = "#000";
      }
    } else {
      costColor = "#fff";
    }
    if (leaderBorder === "rainbow") {
      backgroundImage = `linear-gradient(to right, #000,  #000)`;
      // Always use the predefined rainbow gradient regardless of selected colors
      costStyles = `
        conic-gradient(
          #B31E1F 0% 16.17%,
          black 16.17% 16.67%,
          #258E6A 16.67% 33.17%,
          black 33.17% 33.67%,
          #2082BB 33.67% 49.17%,
          black 49.17% 49.67%,
          #7F3C85 49.67% 66.17%,
          black 66.17% 66.67%,
          #272424 66.67% 83.17%,
          black 83.17% 83.67%,
          #FFEE33 83.67% 99.17%,
          black 99.17% 100%
        )
      `;
    }
  }
  if (event) {
    if (cost === "0") {
      eventMarginLeft = "4%";
    }
    if (cost === "1") {
      eventMarginLeft = "5.0%";
    }
    if (cost === "4" || cost === "3" || cost === "5" || Number(cost) === 11) {
      eventMarginLeft = "3.7%";
    }
    if (cost === "6" || cost === "8") {
      eventMarginLeft = "3.9%";
    }
    if (cost === "7") {
      eventMarginLeft = "5.5%";
    }
    if (cost === "9") {
      eventMarginLeft = "4.7%";
    }
    if (Number(cost) === 10) {
      eventMarginLeft = "2.5%";
    }
    if (Number(cost) >= 13) {
      eventMarginLeft = "2.7%";
    }
    if (Number(cost) === 12) {
      eventMarginLeft = "1.7%";
    }
  }
  if (character && cropper) {
    if (event && eventBorder === "eb-02") {
      return null;
    }
    return (
      <div>
        <div
          className={"absolute rounded-full"}
          style={{
            background:
              Array.isArray(colorArray) && colorArray.length > 1
                ? costGradient
                : `linear-gradient(135deg, ${shadowColor},  ${shadowColor})`,
            transform:
              Array.isArray(colorArray) && colorArray.length > 2
                ? "rotate(-30deg)"
                : "",
            left: getPrintReadyDistanceString(
              "2.4%",
              "left-or-right",
              printReady,
            ),
            top: getPrintReadyDistanceString(
              "1.8%",
              "top-or-bottom",
              printReady,
            ),
            height: getPrintReadySizeString("9.6%", printReady),
            width: getPrintReadySizeString("13.38%", printReady),
          }}
        ></div>
        <div
          className={"absolute rounded-full opacity-[13%]"}
          style={{
            background: `linear-gradient(135deg, #000 56%,  #fff 75%)`,
            left: getPrintReadyDistanceString(
              "2.4%",
              "left-or-right",
              printReady,
            ),
            top: getPrintReadyDistanceString(
              "1.8%",
              "top-or-bottom",
              printReady,
            ),
            height: getPrintReadySizeString("9.6%", printReady),
            width: getPrintReadySizeString("13.38%", printReady),
          }}
        ></div>
      </div>
    );
  }
  if (cropper) {
    return null;
  }

  if (leader) {
    return (
      <>
        <div>
          {leaderBorder !== "full-art" && (
            <div
              className={`absolute rounded-full ${leaderBorder === "AA-white" ? "opacity-[30%]" : "opacity-[20%]"}`}
              style={{
                background:
                  leaderBorder === "AA-white"
                    ? `linear-gradient(135deg, #1F1F1F 0% 25%,  #fff 55%)`
                    : `linear-gradient(135deg, #1F1F1F 0% 55%,  #fff 80%)`,
                zIndex: 4,
                filter: "blur(0.05em)",
                right: getPrintReadyDistanceString(
                  "6.90%",
                  "left-or-right",
                  printReady,
                ),
                bottom: getPrintReadyDistanceString(
                  "9.825%",
                  "top-or-bottom",
                  printReady,
                ),
                height: printReady
                  ? getPrintReadySizeString("6.6521%", printReady)
                  : "6.5251%",

                width: getPrintReadySizeString("9.0945%", printReady),
              }}
            ></div>
          )}
          <div
            className={"absolute rounded-full"}
            style={{
              background:
                leaderBorder === "rainbow" ? costStyles : costGradient,

              transform:
                leader &&
                (leaderBorder === "rainbow" ||
                  (Array.isArray(colorArray) && colorArray.length > 2))
                  ? "rotate(-30deg)"
                  : "",
              zIndex: 4,
              right: getPrintReadyDistanceString(
                "7.40%",
                "left-or-right",
                printReady,
              ),
              bottom: getPrintReadyDistanceString(
                "10.085%",
                "top-or-bottom",
                printReady,
              ),
              height: printReady
                ? getPrintReadySizeString("5.9521%", printReady)
                : "5.8251%",
              width: getPrintReadySizeString("8.1187%", printReady),
            }}
          ></div>
          <div
            className={"absolute rounded-full opacity-[13%]"}
            style={{
              background: `linear-gradient(135deg, #1F1F1F 0%,  #fff 75%)`,
              zIndex: 4,
              right: getPrintReadyDistanceString(
                "7.40%",
                "left-or-right",
                printReady,
              ),
              bottom: getPrintReadyDistanceString(
                "10.085%",
                "top-or-bottom",
                printReady,
              ),
              height: printReady
                ? getPrintReadySizeString("5.9521%", printReady)
                : "5.8251%",
              width: getPrintReadySizeString("8.1187%", printReady),
            }}
          ></div>
        </div>

        <p
          className={`font-open-sans absolute w-auto font-bold ${
            leaderBorder === "AA-white"
              ? "text-neutral-950 [text-shadow:-0.0300em_-0.0300em_0_#fff,0.0300em_-0.0300em_0_#fff,-0.0300em_0.0300em_0_#fff,0.0300em_0.0300em_0_#fff]"
              : ""
          } ${leaderBorder === "full-art" ? "text-outline-xxl text-outline text-neutral-950" : ""} `}
          style={{
            backgroundImage:
              leaderBorder === "full-art"
                ? "linear-gradient(to right, #fff,  #fff)"
                : "",
            zIndex: 4,
            right: getPrintReadyDistanceString(
              "9.05%",
              "left-or-right",
              printReady,
            ),

            bottom: printReady
              ? getPrintReadyDistanceString(
                  "7.07%",
                  "top-or-bottom",
                  printReady,
                )
              : "6.98%",
            height: getPrintReadySizeString("3.2151%", printReady),
            fontSize: getPrintReadySizeString("101%", printReady),
            letterSpacing: `${getPrintReadySizeNumber(-0.008, printReady)}em`,
          }}
        >
          LIFE
        </p>
        <div
          className={`absolute`}
          style={{
            right: getPrintReadyDistanceString(
              leaderStyles.right,
              "left-or-right",
              printReady,
            ),

            bottom: getPrintReadyDistanceString(
              leaderStyles.bottom,
              "top-or-bottom",
              printReady,
            ),
          }}
        >
          <p
            style={{
              fontSize: `${fontsize}px`,
              marginLeft: `${getPrintReadySizeNumber(padding, printReady)}px`,
              zIndex: 5,
              color: costColor,
              top: !leader
                ? getPrintReadyDistanceString(
                    costOffsetStandard.top,
                    "top-or-bottom",
                    printReady,
                  )
                : "",
              left: !leader
                ? getPrintReadyDistanceString(
                    costOffsetStandard.left,
                    "left-or-right",
                    printReady,
                  )
                : "",
              letterSpacing: !leader
                ? `${getPrintReadySizeNumber(costOffsetStandard.tracking, printReady)}`
                : "",
            }}
            className="font-one-piece absolute rounded-full text-black"
          >
            <span
              className={`cost-outline text-outline-xs absolute block px-[0.029em]!`}
              style={{
                backgroundImage: backgroundImage,
              }}
            >
              {`${cost === "0" ? ":" : cost === "1" ? ";" : cost}`}
            </span>
          </p>
        </div>
      </>
    );
  } else if (eventOrStage && event && eventBorder === "eb-02") {
    return (
      <div>
        {/*Cost Background*/}
        <div
          className={"absolute"}
          style={{
            maskImage: `url(${eventCostBackground})`,
            maskSize: "cover",
            maskRepeat: "no-repeat",
            background: `linear-gradient(135deg, #FAF7EF, #FAF7EF)`,
            // `linear-gradient(135deg, #000, #000)`,

            zIndex: 4,
            left: printReady
              ? getPrintReadyDistanceString("1.8%", "left-or-right", printReady)
              : getPrintReadyDistanceString(
                  "1.5%",
                  "left-or-right",
                  printReady,
                ),
            top: printReady
              ? getPrintReadyDistanceString("2.4%", "top-or-bottom", printReady)
              : "2.3%",
            height: printReady
              ? getPrintReadySizeString("12.4%", printReady)
              : "12.4%",
            width: getPrintReadySizeString("16.38%", printReady),
          }}
        ></div>

        {/*Cost Frame*/}
        <div
          className={"absolute"}
          style={{
            maskImage: `url(${eventCostFrame})`,
            maskSize: "cover",
            maskRepeat: "no-repeat",
            background: costGradient,

            zIndex: 4,
            left: getPrintReadyDistanceString(
              "1.8%",
              "left-or-right",
              printReady,
            ),
            top: printReady
              ? getPrintReadyDistanceString("2.3%", "top-or-bottom", printReady)
              : "2.3%",
            height: printReady
              ? getPrintReadySizeString("12.45%", printReady)
              : "12.2%",
            width: getPrintReadySizeString("16.58%", printReady),
          }}
        ></div>

        <p
          style={{
            fontSize: `${eventFontsize}px`,
            marginLeft: eventMarginLeft,
            zIndex: 4,
            top: getPrintReadyDistanceString("1%", "top-or-bottom", printReady),
            left: printReady
              ? getPrintReadyDistanceString("2.5%", "left-or-right", printReady)
              : getPrintReadyDistanceString("3%", "left-or-right", printReady),
            color: colorArray.length > 1 ? "#000" : shadowColor,
          }}
          className={`font-event-cost absolute rounded-full`}
        >
          <span className={`cost-outline absolute block`}>{cost}</span>
        </p>
      </div>
    );
  } else {
    return (
      <div>
        {/*Cost Background*/}
        <div
          className={"absolute rounded-full"}
          style={{
            background:
              Array.isArray(colorArray) && colorArray.length > 1
                ? costGradient
                : `linear-gradient(135deg, ${shadowColor},  ${shadowColor})`,
            transform:
              Array.isArray(colorArray) && colorArray.length > 2
                ? "rotate(-30deg)"
                : "",
            zIndex: 4,
            left: getPrintReadyDistanceString(
              "2.4%",
              "left-or-right",
              printReady,
            ),
            top: printReady
              ? getPrintReadyDistanceString(
                  "1.78%",
                  "top-or-bottom",
                  printReady,
                )
              : "1.8%",
            height: printReady
              ? getPrintReadySizeString("9.86%", printReady)
              : "9.6%",
            width: getPrintReadySizeString("13.38%", printReady),
          }}
        ></div>
        {/*Cost Shadow*/}
        <div
          className={"absolute rounded-full"}
          style={{
            background: `linear-gradient(135deg, rgba(255,255,255) 10%,rgba(255,255,255,0) 20% , #000 45%,  #fff 65%)`,
            opacity: `${characterBorder === "none" ? "15%" : "10%"}`,
            left: getPrintReadyDistanceString(
              "2.4%",
              "left-or-right",
              printReady,
            ),
            zIndex: 4,
            top: printReady
              ? getPrintReadyDistanceString(
                  "1.78%",
                  "top-or-bottom",
                  printReady,
                )
              : "1.8%",
            height: printReady
              ? getPrintReadySizeString("9.86%", printReady)
              : "9.6%",
            width: getPrintReadySizeString("13.38%", printReady),
          }}
        ></div>

        <p
          style={{
            fontSize: `${fontsize}px`,
            marginLeft: `${getPrintReadySizeNumber(padding, printReady)}px`,
            zIndex: 4,
            top: !leader
              ? getPrintReadyDistanceString(
                  costOffsetStandard.top,
                  "top-or-bottom",
                  printReady,
                )
              : "",
            left: !leader
              ? getPrintReadyDistanceString(
                  costOffsetStandard.left,
                  "left-or-right",
                  printReady,
                )
              : "",
            letterSpacing: !leader
              ? `${getPrintReadySizeNumber(costOffsetStandard.tracking, printReady)}`
              : "",
          }}
          className={`font-one-piece absolute rounded-full ${
            character &&
            (color.startsWith("yellow") ||
              (Array.isArray(colorArray) &&
                colorArray.some((c) => c.startsWith("yellow")))) &&
            characterBorder === "none"
              ? "text-white"
              : (color.startsWith("yellow") && colorArray.length === 1) ||
                  (Array.isArray(colorArray) &&
                    colorArray.length === 1 &&
                    colorArray.some((c) => c.startsWith("yellow")))
                ? "text-black"
                : "text-white"
          }`}
        >
          <span
            className={`cost-outline ${Number(cost) > 10 ? "text-outline-xxs" : "text-outline-xs"} absolute block`}
            style={{
              color:
                character &&
                (characterBorder === "sec" ||
                  characterBorder === "sec-aa" ||
                  characterBorder === "mr")
                  ? `${CHARACTER_COLORS_SEC["gold"]}`
                  : event && eventBorder === "op-10"
                    ? `${CHARACTER_COLORS_SEC["goldEvent"]}`
                    : "",
              backgroundImage: backgroundImage,
              paddingRight: `${getPrintReadySizeNumber(0.029, printReady)}em`,
              paddingLeft: `${getPrintReadySizeNumber(0.029, printReady)}em`,
              letterSpacing: letterSpacing,
            }}
          >
            {`${cost === "0" ? ":" : cost === "1" ? ";" : cost}`}
          </span>
        </p>
      </div>
    );
  }
}

function getCharacterOffsetStyles(cost: string) {
  const costNumber = Number(cost);
  const offsets = [
    { left: "1.9%", top: "1.05%" },
    { left: "2.225%", top: "1.25%" },
    { left: "1.9%", top: "1.3%" },
    { left: "1.85%", top: "1.15%" },
    { left: "1.4%", top: "1.2%" },
    { left: "1.75%", top: "1.3%" },
    { left: "1.85%", top: "1.2%" },
    { left: "2.15%", top: "1.4%" },
    { left: "1.95%", top: "1.25%" },
    { left: "1.85%", top: "1.15%" },
    { left: "-0.10%", tracking: -0.035, top: "1.15%" },
    { left: "-0.10%", tracking: -0.035, top: "1.15%" },
    { left: "-0.7%", tracking: -0.035, top: "1.15%" },
    { left: "-0.7%", tracking: -0.035, top: "1.15%" },
  ];
  if (costNumber === 0) return offsets[0];
  // if (costNumber > 0 && costNumber <= 9 && costNumber !== 4) return offsets[1];
  if (costNumber === 1) return offsets[1];
  if (costNumber === 2) return offsets[2];
  if (costNumber === 3) return offsets[3];
  if (costNumber === 4) return offsets[4];
  if (costNumber === 5) return offsets[5];
  if (costNumber === 6) return offsets[6];
  if (costNumber === 7) return offsets[7];
  if (costNumber === 8) return offsets[8];
  if (costNumber === 9) return offsets[9];
  if (costNumber === 10) return offsets[10];
  if (costNumber === 11) return offsets[11];
  if (costNumber === 12) return offsets[12];
  if (costNumber >= 13) return offsets[13];

  // if (costNumber === 5 || costNumber === 8 || costNumber == 7)
  //   return offsets[5];

  return { left: "", tracking: 0, top: "" };
}
