"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { CharacterBorder } from "@/types";

const counter = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/CounterV5.png`;
const counterMulticolor = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Counter-Multicolor.png`;
const optimizedCounterMulticolor = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Counter-Multicolor.webp`;
const printReadyCounterMulticolor = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Counter-Multicolor.png`;
const optimizedPrintReadyCounterMulticolor = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Counter-Multicolor.webp`;
const printReadyCounter = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-CounterV5.png`;
import { getCardColor } from "@/app/helpers/getCardColor";
import { generateMultiColorGradient } from "@/app/helpers/generateMultiColorGradient";

export default function CardCounter({ png = false }: { png?: boolean }) {
  const printReady = useGetStoreState("printReady");
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const characterBorder = useGetStoreState(
    "characterBorder",
  ) as CharacterBorder;
  const counterText = useGetStoreState("counterText");
  const characterBorderEnabled = useGetStoreState("leaderBorderEnabled");
  const shadowColor = getCardColor(color, characterBorder === "none");
  if (
    counterText.length > 0 &&
    characterBorder !== "none" &&
    characterBorder !== "sp-v2"
  ) {
    if (colorArray.length > 2) {
      return (
        <div
          className={`border-bottom-gradient absolute top-0`}
          style={{
            maskImage: `url(${png ? (printReady ? printReadyCounterMulticolor : counterMulticolor) : printReady ? optimizedPrintReadyCounterMulticolor : optimizedCounterMulticolor})`,
            background: generateMultiColorGradient(colorArray),
            backgroundBlendMode: "soft-light",
            zIndex: !characterBorderEnabled ? "0" : "4",
          }}
        ></div>
      );
    }
    return (
      <>
        <div
          className={"counter-gradient absolute"}
          style={{
            background: `linear-gradient(to right,${shadowColor},  ${shadowColor})`,
            maskImage: `url(${printReady ? printReadyCounter : counter})`,
            maskSize: "cover",
            maskRepeat: "no-repeat",
            zIndex: !characterBorderEnabled ? "0" : "4",
            left: printReady ? `3.945155289638293%` : "4.3%",
            top: printReady ? `29.38474875274396%` : "31.358%",
            height: printReady ? `30.047960293717703%` : "28.75%",
            width: printReady ? `13.012268153385913%` : "4.80%",
          }}
        ></div>
      </>
    );
  }

  return null;
}
//
