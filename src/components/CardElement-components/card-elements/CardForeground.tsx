"use client";
import React from "react";
import { useGetStoreState } from "@/helpers/useGetStoreState";

export default function CardForeground({ zIndex = 0 }: { zIndex?: number }) {
  const imageUrl = useGetStoreState("imageUrl") as string;

  if (imageUrl) {
    return (
      <img
        className={"absolute h-full w-full object-cover"}
        src={imageUrl}
        alt={"your image"}
        style={{ zIndex: zIndex }}
      />
    );
  }
  return null;
}
