"use client";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";
import React from "react";

export default function CharacterBorder() {
  const characterBorderEnabled = useGetStoreState("leaderBorderEnabled");
  if (characterBorderEnabled) {
    return (
      <CardBorder
        cardType={"character"}
        quality={25}
        absolute={true}
        zIndex={4}
      />
    );
  }
  return null;
}
