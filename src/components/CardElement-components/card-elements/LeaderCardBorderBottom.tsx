"use client";
import Image from "next/image";

import { useGetStoreState } from "@/helpers/useGetStoreState";
import { getCardType } from "@/helpers/getCardType";
const borderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Card-Border-Bottom.png`;
const optimizedBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Card-Border-Bottom.webp`;
const printReadyBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Card-Border-Bottom.png`;
const optimizedPrintReadyBorderBottom = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Card-Border-Bottom.webp`;

const leaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Leader-Inner-Border-Bottom.png`;
const optimizedLeaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Leader-Inner-Border-Bottom.webp`;
const printReadyLeaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Leader-Inner-Border-Bottom.png`;
const optimizedPrintReadyLeaderInnerBorder = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Leader-Inner-Border-Bottom.webp`;

const leaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Leader-Inner-Border-Bottom-Pattern-New.png`;
const optimizedLeaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Leader-Inner-Border-Bottom-Pattern-New.webp`;
const printReadyLeaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Leader-Inner-Border-Bottom-Pattern-New.png`;
const optimizedPrintReadyLeaderBorderPattern = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Leader-Inner-Border-Bottom-Pattern-New.webp`;

const innerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Inner-Border-Bottom-Shadow-v2.png`;
const optimizedInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Inner-Border-Bottom-Shadow-v2.webp`;
const printReadyInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/Print-Ready-Inner-Border-Bottom-Shadow-v2.png`;
const optimizedPrintReadyInnerBorderBottomShadow = `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/Print-Ready-Inner-Border-Bottom-Shadow-v2.webp`;
import getLeaderBorderGradient from "@/app/helpers/getLeaderBorderGradient";
import getMultiColorBorderGradient from "@/app/helpers/getMultiColorBorderGradient";
import { generatePatternGradient } from "@/app/helpers/generatePatternGradient";
import { LeaderBorder } from "@/types";

export default function LeaderCardBorderBottom({
  absolute = false,
  cardType,
  quality,
  png = false,
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  quality: number;
  png?: boolean;
  absolute?: boolean;
}) {
  const printReady = useGetStoreState("printReady");
  const cardTypeRoute = getCardType(cardType);
  let foilBorderRoute;
  if (cardType === "event") {
    foilBorderRoute = "EventFoil";
  }
  if (cardType === "stage") {
    foilBorderRoute = "StageFoil";
  }
  const colorArray = useGetStoreState("colorArray");
  const color = colorArray[0];
  const color2 = colorArray[colorArray.length - 1];
  const foilBorder = useGetStoreState("foilBorder");
  const leaderBorderEnabled = useGetStoreState("leaderBorderEnabled");

  const leaderBorder = useGetStoreState("leaderBorder") as LeaderBorder;

  // Use the multi-color border gradient function if we have more than 2 colors
  let [backgroundGradient, backgroundBlendMode, patternGradient] =
    (Array.isArray(colorArray) &&
      colorArray.length > 2 &&
      cardType !== "leader") ||
    (Array.isArray(colorArray) &&
      colorArray.length > 2 &&
      cardType === "leader")
      ? getMultiColorBorderGradient(colorArray, leaderBorder, cardType)
      : getLeaderBorderGradient(color, color2, leaderBorder, cardType);
  backgroundGradient = backgroundGradient;

  // For multiple colors, set the appropriate blend mode
  if (
    (Array.isArray(colorArray) &&
      colorArray.length >= 2 &&
      color !== color2 &&
      cardType !== "leader") ||
    (Array.isArray(colorArray) &&
      colorArray.length >= 3 &&
      color !== color2 &&
      cardType === "leader")
  ) {
    // Use screen for 2 colors, and soft-light for 3+ colors for better blending with our overlapping gradients
    backgroundBlendMode = colorArray.length === 2 ? "screen" : "soft-light";
  }

  // Generate a custom pattern gradient for multiple colors
  if (Array.isArray(colorArray) && colorArray.length > 2) {
    patternGradient = generatePatternGradient(colorArray);
  }

  return (
    <>
      {!leaderBorderEnabled && cardType === "leader" && (
        <>
          <div
            className={`border-bottom-gradient top-0 ${absolute ? "absolute" : "relative"} ${
              leaderBorder === "standard" ||
              leaderBorder === "AA-white" ||
              leaderBorder === "AA-black-and-white" ||
              leaderBorder === "standard-white"
                ? "opacity-100"
                : "opacity-0"
            } `}
            style={{
              maskImage: `url(${png ? (printReady ? printReadyBorderBottom : borderBottom) : printReady ? optimizedPrintReadyBorderBottom : optimizedBorderBottom})`,
              background: backgroundGradient,
              backgroundBlendMode: backgroundBlendMode,
              zIndex: 4,
            }}
          ></div>

          {leaderBorder === "rainbow" && (
            <div
              className={`border-bottom-gradient top-0 ${absolute ? "absolute" : "relative"} `}
              style={{
                maskImage: `url(${png ? (printReady ? printReadyBorderBottom : borderBottom) : printReady ? optimizedPrintReadyBorderBottom : optimizedBorderBottom})`,
                background: backgroundGradient,
                backgroundBlendMode: backgroundBlendMode,
                zIndex: 4,
              }}
            ></div>
          )}

          <>
            {/*            <div
              className={`border-gradient-shadow top-0 ${absolute ? "absolute" : "relative"} opacity-[15%]
          `}
              style={{
                maskImage: `url(${leaderInnerBorder})`,
                background: `linear-gradient(to right,#000 ,#000)`,
                zIndex: 4,
              }}
            ></div>*/}
            {leaderBorder === "standard-white" && (
              <Image
                width={printReady ? 3677 : 3357}
                height={printReady ? 5011 : 4692}
                priority
                loading={"eager"}
                title={`${cardTypeRoute} card border`}
                className={`absolute top-0 max-w-full`}
                src={
                  png
                    ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Black-White-Border-Bottom-Pattern.png`
                    : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/optimized/${printReady ? "Print-Ready-" : ""}Black-White-Border-Bottom-Pattern.webp`
                }
                alt={`${cardTypeRoute} card border`}
                quality={quality}
                style={{ zIndex: 4 }}
                sizes={
                  quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""
                }
              />
            )}
            {leaderBorder !== "full-art" && (
              <>
                <div
                  className={`border-gradient-shadow top-0 ${absolute ? "absolute" : "relative"} opacity-[15%]`}
                  style={{
                    maskImage: `url(${png ? (printReady ? printReadyInnerBorderBottomShadow : innerBorderBottomShadow) : printReady ? optimizedPrintReadyInnerBorderBottomShadow : optimizedInnerBorderBottomShadow})`,
                    background: `linear-gradient(to right,#000 ,#000)`,
                    zIndex: 4,
                  }}
                ></div>

                <div
                  className={`border-bottom-gradient top-0 ${absolute ? "absolute" : "relative"} `}
                  style={{
                    maskImage: `url(${png ? (printReady ? printReadyLeaderInnerBorder : leaderInnerBorder) : printReady ? optimizedPrintReadyLeaderInnerBorder : optimizedLeaderInnerBorder})`,
                    background: `linear-gradient(to right, ${leaderBorder === "AA-white" ? "#fff" : "#080809"},  ${leaderBorder === "AA-white" ? "#fff" : "#080809"})`,
                    zIndex: 4,
                  }}
                ></div>
                <div
                  className={`border-bottom-gradient top-0 ${absolute ? "absolute" : "relative"} ${leaderBorder === "AA-white" ? "opacity-70" : leaderBorder === "AA-black" || leaderBorder === "AA-black-and-white" ? "opacity-50" : "opacity-0"} `}
                  style={{
                    maskImage: `url(${png ? (printReady ? printReadyLeaderBorderPattern : leaderBorderPattern) : printReady ? optimizedPrintReadyLeaderBorderPattern : optimizedLeaderBorderPattern})`,
                    background: patternGradient,
                    backgroundBlendMode: backgroundBlendMode,
                    zIndex: 4,
                  }}
                ></div>
              </>
            )}
          </>
        </>
      )}

      {(cardType === "stage" || cardType === "event") && foilBorder && (
        <Image
          width={printReady ? 3677 : 3357}
          height={printReady ? 5011 : 4692}
          priority
          loading={"eager"}
          title={`${cardTypeRoute} ${color} card border`}
          className={"absolute top-0 max-w-full"}
          src={
            png
              ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/${cardTypeRoute}/$${printReady ? "Print-Ready-" : ""}${foilBorderRoute}BorderBottom.png`
              : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/${cardTypeRoute}/optimized/${foilBorderRoute}BorderBottom.webp`
          }
          alt={`${cardTypeRoute} ${color} card border`}
          quality={quality}
          sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
        />
      )}
      {cardType === "don" && (
        <Image
          width={printReady ? 3677 : 3357}
          height={printReady ? 5011 : 4692}
          priority
          loading={"eager"}
          title={`${cardTypeRoute} ${color} card border`}
          className={`${absolute ? "absolute" : "relative"} top-0 max-w-full`}
          src={
            png
              ? `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/${printReady ? "Print-Ready-" : ""}${cardTypeRoute}/donBorder.png`
              : `${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/${cardTypeRoute}/optimized/donBorder.webp`
          }
          alt={`${cardTypeRoute} ${color} card border`}
          quality={quality}
          sizes={quality !== 100 ? "(max-width: 1400px) 100vw, 1400px" : ""}
        />
      )}
      <Image
        width={printReady && png ? 3677 : 3357}
        height={printReady && png ? 5011 : 4692}
        priority
        loading={"eager"}
        title={`card border`}
        className={`relative top-0 max-w-full opacity-0`}
        src={`${process.env.NEXT_PUBLIC_ASSETS_URL}/assets/Border-Assets/${printReady ? "Print-Ready-" : ""}Leader-Inner-Border-Bottom.png`}
        alt={`card border`}
        quality={0}
        sizes={""}
      />
    </>
  );
}
