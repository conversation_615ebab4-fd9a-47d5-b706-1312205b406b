import { CharacterBorder } from "@/types";

export const CHARACTER_BORDER_TYPES: {
  value: CharacterBorder;
  label: string;
}[] = [
  { value: "standard", label: "Standard" },
  { value: "rare", label: "No border" },
  { value: "sr", label: "Super rare" },
  { value: "sec", label: "Secret rare" },
  { value: "sec-aa", label: "Secret rare (AA)" },
  { value: "mr", label: "Manga rare" },
  { value: "none", label: "SP" },
  { value: "sp-v2", label: "SP V2" },
];
export const CHARACTER_COLORS_SP = {
  red: "#D61007",
  redDim: "#800A04",
  green: "#0B8D71",
  greenDim: "#086652",
  blue: "#008DD7",
  blueDim: "#005380",
  yellow: "#FFF340",
  yellowDim: "#807A20",
  purple: "#A33097",
  purpleDim: "#85267C",
  black: "#303030",
  blackDim: "#000",
  yellowText: "#D3B305",
};
export type EventColorKey = keyof typeof EVENT_COLORS_EB02;
export const EVENT_COLORS_EB02 = {
  red: "#D61007",
  redDim: "#ce2020",
  green: "#0B8D71",
  greenDim: "#098C69",
  blue: "#278FB7",
  blueDim: "#087BA0",
  yellow: "#e9c31f",
  yellowDim: "#e6bc06",
  purple: "#78266f",
  purpleDim: "#85267C",
  black: "#000",
  blackDim: "#000",
  yellowText: "#D3B305",
};
export const CHARACTER_COLORS = {
  red: "#B31E1F",
  green: "#228D6B",
  blue: "#2082BB",
  yellow: "#FFEE33",
  purple: "#7F3C85",
  black: "#272424",
  yellowText: "#D3B305",
};
export const CHARACTER_COLORS_SEC = {
  gold: "#D5AB06",
  // gold: "#D5AB06",
  goldEvent: "#F8DA1E",
};
