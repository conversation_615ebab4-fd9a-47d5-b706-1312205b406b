"use client";
import { useState, useRef, useEffect } from "react";
import <PERSON><PERSON><PERSON>, { Area } from "react-easy-crop";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useDispatch } from "react-redux";
import {
  onBackgroundImageUrl,
  onImageUrl,
  onIsCropping,
  onIsCroppingBackground,
} from "@/store/formSlice";
import CardBorder from "@/components/CardElement-components/card-elements/CardBorder";
import CardCost from "@/components/CardElement-components/card-elements/CardCost";
import CardColorWheel from "@/components/CardElement-components/card-elements/CardColorWheel";
import { Button } from "@/components/ui/button";

export function ImageCropper({
  cardType,
  imageType = "foreground",
}: {
  cardType: "character" | "leader" | "event" | "stage" | "don";
  imageType?: "foreground" | "background";
}) {
  const dispatch = useDispatch();
  const imageFileStateKey =
    imageType === "foreground" ? "imageFile" : "backgroundImageFile";
  const isCroppingStateKey =
    imageType === "foreground" ? "isCropping" : "isCroppingBackground";
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);

  const [fileUrl, setFileUrl] = useState<string | null>(null); // State to hold the Data URL
  const [fileType, setFileType] = useState<string | null>(null); // State to hold the Data URL

  const [croppedArea, setCroppedArea] = useState<Area | null>(null); // State to hold cropped File
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<Area | null>(null); // State to hold cropped File
  const isCropping = useGetStoreState(isCroppingStateKey);
  const imageFile = useGetStoreState(imageFileStateKey) as File | null;
  const canvas = useRef<HTMLCanvasElement | null>(null);
  useEffect(() => {
    canvas.current = document.createElement("canvas");
  }, []);
  const onCropComplete = () => {
    if (croppedArea && croppedAreaPixels && fileUrl) {
      // Perform cropping and set cropped data in the state

      const image = new Image();
      image.src = fileUrl;
      image.onload = () => {
        if (canvas.current) {
          canvas.current.width = croppedAreaPixels.width;
          canvas.current.height = croppedAreaPixels.height;
          const ctx = canvas.current.getContext("2d");
          ctx?.drawImage(
            image,
            croppedAreaPixels.x,
            croppedAreaPixels.y,
            croppedAreaPixels.width,
            croppedAreaPixels.height,
            0,
            0,
            croppedAreaPixels.width,
            croppedAreaPixels.height,
          );
          const croppedDataURL = canvas.current.toDataURL(
            fileType ? fileType : "image/png",
          );

          if (imageType === "foreground") {
            dispatch(onImageUrl(croppedDataURL));
          }
          if (imageType === "background") {
            dispatch(onBackgroundImageUrl(croppedDataURL));
          }
        }
      };
      if (isCroppingStateKey === "isCropping") {
        dispatch(onIsCropping(false));
      }
      if (isCroppingStateKey === "isCroppingBackground") {
        dispatch(onIsCroppingBackground(false));
      }
    }
  };
  function onCrop(croppedArea: Area, croppedAreaPixels: Area) {
    setCroppedArea(croppedArea);
    setCroppedAreaPixels(croppedAreaPixels);
  }

  const startCrop = () => {
    loadImage(imageFile!);
    if (isCroppingStateKey === "isCropping") {
      dispatch(onIsCropping(true));
    }
    if (isCroppingStateKey === "isCroppingBackground") {
      dispatch(onIsCroppingBackground(true));
    }
  };

  const loadImage = (file: File) => {
    setFileType(file.type);
    const reader = new FileReader();
    reader.onload = (e) => {
      if (e?.target?.result !== fileUrl) {
        setFileUrl(e?.target?.result as string);
        if (imageType === "foreground") {
          dispatch(onImageUrl(e?.target?.result as string));
        }
        if (imageType === "background") {
          dispatch(onBackgroundImageUrl(e?.target?.result as string));
        }
      }
    };
    reader.readAsDataURL(file);
  };

  return (
    <>
      {isCropping && fileUrl ? (
        <>
          <Button
            onClick={onCropComplete}
            className={
              "bg-full-button hover:bg-full-button-hover dark:bg-full-button-dark dark:hover:bg-full-button-hover-dark font-medium! tracking-wide active:translate-y-0.5"
            }
            color={"green"}
          >
            Complete Cropping
          </Button>
          <div className={"relative overflow-clip rounded-xl sm:max-w-[19rem]"}>
            <Cropper
              image={fileUrl}
              crop={crop}
              zoom={zoom}
              minZoom={0.1}
              maxZoom={10}
              aspect={3357 / 4692}
              onCropChange={(point) => {
                setCrop(point);
              }}
              onCropComplete={onCrop}
              restrictPosition={false}
              zoomSpeed={0.1}
              onZoomChange={setZoom}
              objectFit={"cover"}
              style={{
                cropAreaStyle: {
                  zIndex: "10",
                },
              }}
            />

            <CardBorder
              cardType={cardType}
              quality={1}
              className={"opacity-50"}
            />
            <CardCost
              character={cardType !== "leader" && cardType !== "don"}
              leader={cardType === "leader" || cardType === "don"}
              cropper={true}
              event={cardType === "event"}
            />
            <CardColorWheel cardKind={cardType} />
          </div>
        </>
      ) : (
        <>
          {imageFile && (
            <>
              <Button
                onClick={startCrop}
                className={
                  "bg-full-button hover:bg-full-button-hover dark:bg-full-button-dark dark:hover:bg-full-button-hover-dark font-medium! tracking-wide active:translate-y-0.5"
                }
              >
                Crop the image
              </Button>
            </>
          )}
        </>
      )}
    </>
  );
}
