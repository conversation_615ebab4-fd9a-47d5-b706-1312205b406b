// "use client";
//
// import {
//   Canvas,
//   FabricObject,
//   FabricObjectProps,
//   Gradient,
//   loadSVGFromURL,
//   ObjectEvents,
//   SerializedObjectProps,
//   Text,
//   util,
// } from "fabric";
// import { useEffect, useRef } from "react";
//
// export default function FabricCanvas() {
//   const canvasRef = useRef<HTMLCanvasElement | null>(null);
//
//   useEffect(() => {
//     if (!canvasRef.current) return;
//
//     // Create Fabric.js Canvas (interactive)
//     const canvas = new Canvas(canvasRef.current);
//
//     // Create text
//     const helloWorld = new Text("Hello world!", {
//       left: 100,
//       top: 100,
//       backgroundColor: "red",
//     });
//     loadSVGFromURL("/assets/svg/Border-Assets/Card-Background.svg").then(
//       ({ objects }) => {
//         // Filter out null values from the objects array
//         const nonNullObjects = objects.filter(
//           (
//             element,
//           ): element is FabricObject<
//             Partial<FabricObjectProps>,
//             SerializedObjectProps,
//             ObjectEvents
//           > => element !== null,
//         );
//
//         if (nonNullObjects.length === 0) return;
//         // nonNullObjects.forEach((obj) => {
//         //   obj.set(
//         //     "fill",
//         //     new Gradient({
//         //       type: "linear",
//         //       gradientUnits: "percentage", // or "pixels"
//         //       coords: { x1: 0, y1: 0, x2: 0, y2: 1 }, // vertical gradient
//         //       colorStops: [
//         //         { offset: 0, color: "green" },
//         //         { offset: 1, color: "yellow" },
//         //       ],
//         //     }),
//         //   );
//         // });
//         const group = util.groupSVGElements(nonNullObjects, {
//           scaleY: 0.3,
//           scaleX: 0.3,
//         });
//         const { width, height } = group.getBoundingRect();
//
//         group.set(
//           "fill",
//           new Gradient({
//             type: "linear",
//             gradientUnits: "pixels", // important: use absolute coords
//             coords: { x1: 0, y1: 0, x2: width, y2: 0 }, // left → right
//             colorStops: [
//               { offset: 0, color: "green" },
//               { offset: 1, color: "blue" },
//             ],
//           }),
//         );
//         group.set(
//           "stroke",
//           new Gradient({
//             type: "linear",
//             gradientUnits: "pixels", // important: use absolute coords
//             coords: { x1: 0, y1: 0, x2: width, y2: 0 }, // left → right
//             colorStops: [
//               { offset: 0, color: "green" },
//               { offset: 1, color: "blue" },
//             ],
//           }),
//         );
//
//         canvas.add(group);
//         canvas.renderAll();
//       },
//     );
//
//     canvas.add(helloWorld);
//     canvas.centerObject(helloWorld);
//     canvas.renderAll();
//
//     // Cleanup on unmount
//     return () => {
//       canvas.dispose();
//     };
//   }, []);
//   return (
//     <canvas
//       ref={canvasRef}
//       width={800}
//       height={600}
//       style={{ border: "1px solid #ccc" }}
//     />
//   );
// }
