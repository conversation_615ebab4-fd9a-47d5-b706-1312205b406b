"use client";
import React, { useMemo } from "react";
import {
  Text,
  Group,
  Rect,
  Shape,
  Circle,
  Ring,
  Image as KonvaImage,
} from "react-konva";
import { useGetStoreState } from "@/helpers/useGetStoreState";
import { useImage } from "react-konva-utils";

interface TextSegment {
  text: string;
  styles: {
    color?: string;
    backgroundColor?: string;
    fontWeight?: string;
    fontSize?: number;
    fontFamily?: string;
    borderRadius?: number;
    padding?: { x: number; y: number };
    margin?: { x: number; y: number };
    isAbility?: boolean;
    abilityType?: string;
    border?: string;
  };
}

interface RichTextRendererProps {
  htmlContent: string;
  x?: number;
  y?: number;
  width?: number;
  fontSize?: number;
  fontFamily?: string;
  color?: string;
}

// Ability type styles mapping
const ABILITY_STYLES = {
  "blue-ability": {
    backgroundColor: "#2F77B3",
    color: "#ffffff",
    fontSize: 0.85,
    fontWeight: "400",
    padding: { x: 0.5, y: 0.25 },
  },
  "pink-ability": {
    backgroundColor: "#d94880",
    color: "#ffffff",
    fontSize: 0.85,
    fontWeight: "400",
    padding: { x: 0.5, y: 0.25 },
  },
  "orange-ability": {
    backgroundColor: "#DC8535",
    color: "#ffffff",
    fontSize: 0.85,
    fontWeight: "300",
    padding: { x: 0.5, y: 0.25 },
  },
  "red-ability": {
    backgroundColor: "#ba212f",
    color: "#ffffff",
    fontSize: 0.85,
    fontWeight: "300",
    padding: { x: 0.5, y: 0.25 },
  },
  "trigger-ability": {
    backgroundColor: "#FDEA42",
    color: "#1c1917",
    fontSize: 0.9,
    fontWeight: "300",
    padding: { x: 0.4, y: 0.25 },
  },
  "black-ability": {
    backgroundColor: "#000000",
    color: "#ffffff",
    fontSize: 0.85,
    fontWeight: "400",
    padding: { x: 0.5, y: 0.25 },
  },
  "white-ability": {
    backgroundColor: "#FFFFFF",
    color: "#1c1917",
    fontSize: 0.75,
    fontWeight: "600",
    padding: { x: 0.3, y: 0.3 },
    border: "#1c1917",
  },
};

function parseHtmlToSegments(html: string): TextSegment[] {
  const segments: TextSegment[] = [];

  // Simple HTML parser - this is a basic implementation
  // For production, you might want to use a proper HTML parser
  const parser = new DOMParser();
  const doc = parser.parseFromString(`<div>${html}</div>`, "text/html");

  function processNode(node: Node, inheritedStyles: any = {}): void {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent;
      if (text && text.trim()) {
        // Preserve spaces but handle them properly
        segments.push({
          text: text,
          styles: { ...inheritedStyles },
        });
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();
      const className = element.className;
      const style = element.getAttribute("style") || "";

      let newStyles = { ...inheritedStyles };

      // Parse inline styles
      if (style) {
        const styleObj = parseInlineStyle(style);
        newStyles = { ...newStyles, ...styleObj };
      }

      // Handle ability classes - check for the most specific ability class
      const abilityClasses = Object.keys(ABILITY_STYLES);
      const abilityClass = abilityClasses.find((cls) =>
        className.split(" ").includes(cls),
      );

      if (abilityClass) {
        const abilityStyle =
          ABILITY_STYLES[abilityClass as keyof typeof ABILITY_STYLES];
        newStyles = {
          ...newStyles,
          ...abilityStyle,
          isAbility: true,
          abilityType: abilityClass,
        };
      }

      // Handle container classes (they don't add styles but may contain ability elements)
      const isContainer =
        className.includes("-container") || className.includes("-shadow");

      // Handle list items
      if (tagName === "li") {
        // Add bullet point
        segments.push({
          text: "∙ ",
          styles: {
            ...inheritedStyles,
            fontSize: (inheritedStyles.fontSize || 1) * 0.75,
          },
        });
      }

      // Process child nodes
      Array.from(element.childNodes).forEach((child) => {
        processNode(child, newStyles);
      });

      // Add line breaks for block elements
      if (["p", "li"].includes(tagName)) {
        segments.push({
          text: "\n",
          styles: inheritedStyles,
        });
      }
    }
  }

  const container = doc.querySelector("div");
  if (container) {
    Array.from(container.childNodes).forEach((child) => {
      processNode(child);
    });
  }

  // Clean up segments - remove empty ones and merge consecutive text with same styles
  const cleanedSegments = segments.filter((segment) => segment.text.length > 0);

  return cleanedSegments;
}

// Function to create ability shape backgrounds
function createAbilityShape(
  abilityType: string,
  x: number,
  y: number,
  width: number,
  height: number,
  backgroundColor: string,
  index: number,
  abilityDropShadow: boolean = false,
): React.ReactElement | null {
  switch (abilityType) {
    case "blue-ability": // OnPlay - slight border radius
    case "red-ability": // Counter - similar to OnPlay
      return (
        <Group key={`bg-${index}`}>
          {abilityDropShadow && (
            <Rect
              x={x - 2}
              y={y - 2}
              width={width + 4}
              height={height + 4}
              fill="white"
              cornerRadius={6}
            />
          )}
          <Rect
            x={x}
            y={y}
            width={width}
            height={height}
            fill={backgroundColor}
            cornerRadius={4}
          />
        </Group>
      );

    case "pink-ability": // Once Per Turn - fully rounded
      return (
        <Group key={`bg-${index}`}>
          {abilityDropShadow && (
            <Rect
              x={x - 2}
              y={y - 2}
              width={width + 4}
              height={height + 4}
              fill="white"
              cornerRadius={(height + 4) / 2}
            />
          )}
          <Rect
            x={x}
            y={y}
            width={width}
            height={height}
            fill={backgroundColor}
            cornerRadius={height / 2}
          />
        </Group>
      );

    case "orange-ability": // Blocker - hexagon with pointy left/right edges
      return (
        <Group key={`bg-${index}`}>
          {abilityDropShadow && (
            <Shape
              sceneFunc={(context, shape) => {
                context.beginPath();
                const hexWidth = width + 4;
                const hexHeight = height + 4;
                const edgeWidth = hexHeight * 0.3;
                const shadowX = x - 2;
                const shadowY = y - 2;

                context.moveTo(shadowX + edgeWidth, shadowY);
                context.lineTo(shadowX + hexWidth - edgeWidth, shadowY);
                context.lineTo(shadowX + hexWidth, shadowY + hexHeight / 2);
                context.lineTo(
                  shadowX + hexWidth - edgeWidth,
                  shadowY + hexHeight,
                );
                context.lineTo(shadowX + edgeWidth, shadowY + hexHeight);
                context.lineTo(shadowX, shadowY + hexHeight / 2);
                context.closePath();

                context.fillStrokeShape(shape);
              }}
              fill="white"
            />
          )}
          <Shape
            sceneFunc={(context, shape) => {
              context.beginPath();
              const hexWidth = width;
              const hexHeight = height;
              const edgeWidth = hexHeight * 0.3;

              context.moveTo(x + edgeWidth, y);
              context.lineTo(x + hexWidth - edgeWidth, y);
              context.lineTo(x + hexWidth, y + hexHeight / 2);
              context.lineTo(x + hexWidth - edgeWidth, y + hexHeight);
              context.lineTo(x + edgeWidth, y + hexHeight);
              context.lineTo(x, y + hexHeight / 2);
              context.closePath();

              context.fillStrokeShape(shape);
            }}
            fill={backgroundColor}
          />
        </Group>
      );

    case "trigger-ability": // Trigger - slight border radius on left, clipped on right
      return (
        <Group key={`bg-${index}`}>
          {abilityDropShadow && (
            <Shape
              sceneFunc={(context, shape) => {
                context.beginPath();
                const clipPoint = (width + 4) * 0.75;
                const radius = 8;
                const shadowX = x - 2;
                const shadowY = y - 2;
                const shadowWidth = width + 4;
                const shadowHeight = height + 4;

                context.moveTo(shadowX + radius, shadowY);
                context.lineTo(shadowX + clipPoint, shadowY);
                context.lineTo(
                  shadowX + shadowWidth - 3,
                  shadowY + shadowHeight,
                );
                context.lineTo(shadowX + radius, shadowY + shadowHeight);
                context.quadraticCurveTo(
                  shadowX,
                  shadowY + shadowHeight,
                  shadowX,
                  shadowY + shadowHeight - radius,
                );
                context.lineTo(shadowX, shadowY + radius);
                context.quadraticCurveTo(
                  shadowX,
                  shadowY,
                  shadowX + radius,
                  shadowY,
                );
                context.closePath();

                context.fillStrokeShape(shape);
              }}
              fill="white"
            />
          )}
          <Shape
            sceneFunc={(context, shape) => {
              context.beginPath();
              const clipPoint = width * 0.75;
              const radius = 6;

              context.moveTo(x + radius, y);
              context.lineTo(x + clipPoint, y);
              context.lineTo(x + width - 5, y + height);
              context.lineTo(x + radius, y + height);
              context.quadraticCurveTo(x, y + height, x, y + height - radius);
              context.lineTo(x, y + radius);
              context.quadraticCurveTo(x, y, x + radius, y);
              context.closePath();

              context.fillStrokeShape(shape);
            }}
            fill={backgroundColor}
          />
        </Group>
      );

    case "black-ability": // Don!! - octagon shape
      return (
        <Group key={`bg-${index}`}>
          {abilityDropShadow && (
            <Shape
              sceneFunc={(context, shape) => {
                context.beginPath();
                const octWidth = width + 4;
                const octHeight = height + 4;
                const cornerSize = Math.min(octWidth, octHeight) * 0.2;
                const shadowX = x - 2;
                const shadowY = y - 2;

                context.moveTo(shadowX + cornerSize, shadowY);
                context.lineTo(shadowX + octWidth - cornerSize, shadowY);
                context.lineTo(shadowX + octWidth, shadowY + cornerSize);
                context.lineTo(
                  shadowX + octWidth,
                  shadowY + octHeight - cornerSize,
                );
                context.lineTo(
                  shadowX + octWidth - cornerSize,
                  shadowY + octHeight,
                );
                context.lineTo(shadowX + cornerSize, shadowY + octHeight);
                context.lineTo(shadowX, shadowY + octHeight - cornerSize);
                context.lineTo(shadowX, shadowY + cornerSize);
                context.closePath();

                context.fillStrokeShape(shape);
              }}
              fill="white"
            />
          )}
          <Shape
            sceneFunc={(context, shape) => {
              context.beginPath();
              const octWidth = width;
              const octHeight = height;
              const cornerSize = Math.min(octWidth, octHeight) * 0.2;

              context.moveTo(x + cornerSize, y);
              context.lineTo(x + octWidth - cornerSize, y);
              context.lineTo(x + octWidth, y + cornerSize);
              context.lineTo(x + octWidth, y + octHeight - cornerSize);
              context.lineTo(x + octWidth - cornerSize, y + octHeight);
              context.lineTo(x + cornerSize, y + octHeight);
              context.lineTo(x, y + octHeight - cornerSize);
              context.lineTo(x, y + cornerSize);
              context.closePath();

              context.fillStrokeShape(shape);
            }}
            fill={backgroundColor}
          />
        </Group>
      );

    case "white-ability": // Number - fully rounded with black border and white outline, transparent background
      const centerX = x + width / 2;
      const centerY = y + height / 2;
      const radius = Math.min(width, height) / 2;

      return (
        <Group key={`bg-${index}`}>
          {/* White outer outline */}
          <Circle x={centerX} y={centerY} radius={radius + 3} fill="white" />
          {/* Black border */}
          <Circle x={centerX} y={centerY} radius={radius + 1} fill="black" />
          {/* White inner outline */}
          <Circle x={centerX} y={centerY} radius={radius - 1} fill="white" />
          {/* Transparent background - no fill, just the border structure */}
        </Group>
      );

    default:
      return (
        <Rect
          key={`bg-${index}`}
          x={x}
          y={y}
          width={width}
          height={height}
          fill={backgroundColor}
          cornerRadius={2}
        />
      );
  }
}

function parseInlineStyle(style: string): any {
  const styles: any = {};
  const declarations = style.split(";").filter((decl) => decl.trim());

  declarations.forEach((decl) => {
    const [property, value] = decl.split(":").map((s) => s.trim());

    switch (property) {
      case "color":
        styles.color = value;
        break;
      case "background-color":
        styles.backgroundColor = value;
        break;
      case "font-weight":
        styles.fontWeight = value;
        break;
      case "font-size":
        // Convert em/px to relative size
        if (value.includes("em")) {
          styles.fontSize = parseFloat(value);
        }
        break;
    }
  });

  return styles;
}

export default function RichTextRenderer({
  htmlContent,
  x = 0,
  y = 0,
  width = 400,
  fontSize = 16,
  fontFamily = "Arial",
  color = "#000000",
}: RichTextRendererProps) {
  // Get drop shadow states from Redux store
  const abilityDropShadow = useGetStoreState("abilityDropShadow") as boolean;
  const dropShadow = useGetStoreState("dropShadow") as boolean;

  // Load counter icon
  const [counterIcon] = useImage("/assets/svg/CounterYellow.svg");

  console.log("Drop shadow states:", { abilityDropShadow, dropShadow });
  const segments = useMemo(() => {
    const parsed = parseHtmlToSegments(htmlContent);
    console.log("Parsed segments:", parsed);
    return parsed;
  }, [htmlContent]);

  const renderSegments = () => {
    let currentX = x;
    let currentY = y;
    const lineHeight = fontSize * 1.6;
    const elements: React.ReactElement[] = [];

    segments.forEach((segment, index) => {
      if (segment.text === "\n") {
        currentX = x;
        currentY += lineHeight;
        return;
      }

      // Skip empty or whitespace-only segments, but add proper spacing
      if (!segment.text.trim()) {
        currentX += fontSize * 0.25; // Add small space for whitespace
        return;
      }

      const segmentFontSize = segment.styles.fontSize
        ? fontSize * segment.styles.fontSize
        : fontSize;

      const textColor = segment.styles.color || color;

      // Calculate text width (more accurate approximation)
      const charWidth = segmentFontSize * 0.6;
      const textWidth = segment.text.length * charWidth;

      // Check if we need to wrap to next line
      if (currentX + textWidth > x + width && currentX > x) {
        currentX = x;
        currentY += lineHeight;
      }

      // Render background for ability segments
      if (
        segment.styles.isAbility &&
        segment.styles.backgroundColor &&
        segment.styles.abilityType
      ) {
        const paddingX = (segment.styles.padding?.x || 0.3) * segmentFontSize;
        const paddingY = (segment.styles.padding?.y || 0.15) * segmentFontSize;

        // Special padding for trigger ability (extra right padding)
        const rightPadding =
          segment.styles.abilityType === "trigger-ability"
            ? paddingX * 2
            : paddingX;

        // For red-ability (Counter), add space for the icon
        const iconWidth =
          segment.styles.abilityType === "red-ability"
            ? segmentFontSize * 0.64375
            : 0;
        const iconSpacing =
          segment.styles.abilityType === "red-ability"
            ? segmentFontSize * 0.15
            : 0; // margin-right from CSS

        const bgWidth =
          textWidth + paddingX + rightPadding + iconWidth + iconSpacing;
        const bgHeight = segmentFontSize * 1.3 + paddingY * 2;

        // Create the custom shape for this ability type
        const abilityShape = createAbilityShape(
          segment.styles.abilityType,
          currentX - paddingX,
          currentY - paddingY - segmentFontSize * 0.1,
          bgWidth,
          bgHeight,
          segment.styles.backgroundColor,
          index,
          abilityDropShadow,
        );

        if (abilityShape) {
          elements.push(abilityShape);
        }

        // Render text with proper positioning for ability segments
        // Ability text should NOT get text outline (dropShadow only affects non-ability text)

        let textStartX = currentX;

        // Add counter icon for red-ability (inline, before text)
        if (segment.styles.abilityType === "red-ability" && counterIcon) {
          const iconSize = segmentFontSize * 0.64375; // Based on CSS width: 0.64375em
          const iconHeight = segmentFontSize * 0.925; // Based on CSS height: 0.925em
          const iconX = currentX + segmentFontSize * 0.17; // Based on CSS left: 0.17em
          const iconY = currentY - paddingY + segmentFontSize * 0.1; // Based on CSS top: 0.1em

          elements.push(
            <KonvaImage
              key={`counter-icon-${index}`}
              image={counterIcon}
              x={iconX}
              y={iconY}
              width={iconSize}
              height={iconHeight}
            />,
          );

          // Move text to the right of the icon
          textStartX = currentX + iconSize + segmentFontSize * 0.15; // Add margin-right spacing
        }

        // Render main text on top (no outline for ability text)
        elements.push(
          <Text
            key={`text-${index}`}
            x={textStartX}
            y={currentY}
            text={segment.text}
            fontSize={segmentFontSize}
            fontFamily={fontFamily}
            fill={textColor}
            fontStyle={
              segment.styles.fontWeight === "bold" ||
              segment.styles.fontWeight === "600"
                ? "bold"
                : "normal"
            }
          />,
        );

        // Only add extra spacing for trigger ability, normal spacing for others
        const extraSpacing =
          segment.styles.abilityType === "trigger-ability"
            ? segmentFontSize * 0.4
            : segmentFontSize * 0.1;
        currentX += bgWidth + extraSpacing;
      } else {
        // Render regular text (non-ability text can get outline)
        if (dropShadow) {
          // Create white outline by rendering white text at multiple offsets
          const outlineOffsets = [
            { x: -1, y: -1 },
            { x: 0, y: -1 },
            { x: 1, y: -1 },
            { x: -1, y: 0 },
            { x: 1, y: 0 },
            { x: -1, y: 1 },
            { x: 0, y: 1 },
            { x: 1, y: 1 },
          ];

          outlineOffsets.forEach((offset, offsetIndex) => {
            elements.push(
              <Text
                key={`outline-${index}-${offsetIndex}`}
                x={currentX + offset.x}
                y={currentY + offset.y}
                text={segment.text}
                fontSize={segmentFontSize}
                fontFamily={fontFamily}
                fill="white"
                fontStyle={
                  segment.styles.fontWeight === "bold" ||
                  segment.styles.fontWeight === "600"
                    ? "bold"
                    : "normal"
                }
              />,
            );
          });
        }

        // Render main text on top
        elements.push(
          <Text
            key={`text-${index}`}
            x={currentX}
            y={currentY}
            text={segment.text}
            fontSize={segmentFontSize}
            fontFamily={fontFamily}
            fill={textColor}
            fontStyle={
              segment.styles.fontWeight === "bold" ||
              segment.styles.fontWeight === "600"
                ? "bold"
                : "normal"
            }
          />,
        );

        currentX += textWidth + segmentFontSize * 0.1; // Add small spacing
      }
    });

    return elements;
  };

  return <Group>{renderSegments()}</Group>;
}
