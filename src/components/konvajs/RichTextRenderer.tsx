"use client";
import React, { useMemo } from "react";
import { Text, Group, Rect } from "react-konva";

interface TextSegment {
  text: string;
  styles: {
    color?: string;
    backgroundColor?: string;
    fontWeight?: string;
    fontSize?: number;
    fontFamily?: string;
    borderRadius?: number;
    padding?: { x: number; y: number };
    margin?: { x: number; y: number };
    isAbility?: boolean;
    abilityType?: string;
    border?: string;
  };
}

interface RichTextRendererProps {
  htmlContent: string;
  x?: number;
  y?: number;
  width?: number;
  fontSize?: number;
  fontFamily?: string;
  color?: string;
}

// Ability type styles mapping
const ABILITY_STYLES = {
  'blue-ability': {
    backgroundColor: '#2F77B3',
    color: '#ffffff',
    fontSize: 0.9,
    fontWeight: '400',
    padding: { x: 0.4, y: 0.2 },
    borderRadius: 0.3,
  },
  'pink-ability': {
    backgroundColor: '#d94880',
    color: '#ffffff',
    fontSize: 0.9,
    fontWeight: '400',
    padding: { x: 0.4, y: 0.2 },
    borderRadius: 1,
  },
  'orange-ability': {
    backgroundColor: '#DC8535',
    color: '#ffffff',
    fontSize: 0.9,
    fontWeight: '300',
    padding: { x: 0.4, y: 0.2 },
    borderRadius: 0.2,
  },
  'red-ability': {
    backgroundColor: '#ba212f',
    color: '#ffffff',
    fontSize: 0.9,
    fontWeight: '300',
    padding: { x: 0.4, y: 0.2 },
    borderRadius: 0.3,
  },
  'trigger-ability': {
    backgroundColor: '#FDEA42',
    color: '#1c1917',
    fontSize: 0.95,
    fontWeight: '300',
    padding: { x: 0.4, y: 0.2 },
    borderRadius: 0.35,
  },
  'black-ability': {
    backgroundColor: '#000000',
    color: '#ffffff',
    fontSize: 0.9,
    fontWeight: '400',
    padding: { x: 0.4, y: 0.2 },
    borderRadius: 0.2,
  },
  'white-ability': {
    backgroundColor: '#FFFFFF',
    color: '#1c1917',
    fontSize: 0.8,
    fontWeight: '600',
    padding: { x: 0.4, y: 0.2 },
    borderRadius: 0.5,
    border: '#1c1917',
  },
};

function parseHtmlToSegments(html: string): TextSegment[] {
  const segments: TextSegment[] = [];

  // Simple HTML parser - this is a basic implementation
  // For production, you might want to use a proper HTML parser
  const parser = new DOMParser();
  const doc = parser.parseFromString(`<div>${html}</div>`, 'text/html');

  function processNode(node: Node, inheritedStyles: any = {}): void {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent;
      if (text && text.trim()) {
        // Preserve spaces but handle them properly
        segments.push({
          text: text,
          styles: { ...inheritedStyles }
        });
      }
    } else if (node.nodeType === Node.ELEMENT_NODE) {
      const element = node as Element;
      const tagName = element.tagName.toLowerCase();
      const className = element.className;
      const style = element.getAttribute('style') || '';

      let newStyles = { ...inheritedStyles };

      // Parse inline styles
      if (style) {
        const styleObj = parseInlineStyle(style);
        newStyles = { ...newStyles, ...styleObj };
      }

      // Handle ability classes - check for the most specific ability class
      const abilityClasses = Object.keys(ABILITY_STYLES);
      const abilityClass = abilityClasses.find(cls =>
        className.split(' ').includes(cls)
      );

      if (abilityClass) {
        const abilityStyle = ABILITY_STYLES[abilityClass as keyof typeof ABILITY_STYLES];
        newStyles = {
          ...newStyles,
          ...abilityStyle,
          isAbility: true,
          abilityType: abilityClass
        };
      }

      // Handle container classes (they don't add styles but may contain ability elements)
      const isContainer = className.includes('-container') || className.includes('-shadow');

      // Handle list items
      if (tagName === 'li') {
        // Add bullet point
        segments.push({
          text: '∙ ',
          styles: { ...inheritedStyles, fontSize: (inheritedStyles.fontSize || 1) * 0.75 }
        });
      }

      // Process child nodes
      Array.from(element.childNodes).forEach(child => {
        processNode(child, newStyles);
      });

      // Add line breaks for block elements
      if (['p', 'li'].includes(tagName)) {
        segments.push({
          text: '\n',
          styles: inheritedStyles
        });
      }
    }
  }

  const container = doc.querySelector('div');
  if (container) {
    Array.from(container.childNodes).forEach(child => {
      processNode(child);
    });
  }

  // Clean up segments - remove empty ones and merge consecutive text with same styles
  const cleanedSegments = segments.filter(segment => segment.text.length > 0);

  return cleanedSegments;
}

function parseInlineStyle(style: string): any {
  const styles: any = {};
  const declarations = style.split(';').filter(decl => decl.trim());
  
  declarations.forEach(decl => {
    const [property, value] = decl.split(':').map(s => s.trim());
    
    switch (property) {
      case 'color':
        styles.color = value;
        break;
      case 'background-color':
        styles.backgroundColor = value;
        break;
      case 'font-weight':
        styles.fontWeight = value;
        break;
      case 'font-size':
        // Convert em/px to relative size
        if (value.includes('em')) {
          styles.fontSize = parseFloat(value);
        }
        break;
    }
  });
  
  return styles;
}

export default function RichTextRenderer({
  htmlContent,
  x = 0,
  y = 0,
  width = 400,
  fontSize = 16,
  fontFamily = 'Arial',
  color = '#000000'
}: RichTextRendererProps) {
  const segments = useMemo(() => {
    const parsed = parseHtmlToSegments(htmlContent);
    console.log('Parsed segments:', parsed);
    return parsed;
  }, [htmlContent]);
  
  const renderSegments = () => {
    let currentX = x;
    let currentY = y;
    const lineHeight = fontSize * 1.6;
    const elements: React.ReactElement[] = [];

    segments.forEach((segment, index) => {
      if (segment.text === '\n') {
        currentX = x;
        currentY += lineHeight;
        return;
      }

      // Skip empty or whitespace-only segments
      if (!segment.text.trim()) {
        currentX += fontSize * 0.3; // Add small space
        return;
      }

      const segmentFontSize = segment.styles.fontSize
        ? fontSize * segment.styles.fontSize
        : fontSize;

      const textColor = segment.styles.color || color;

      // Calculate text width (more accurate approximation)
      const charWidth = segmentFontSize * 0.6;
      const textWidth = segment.text.length * charWidth;

      // Check if we need to wrap to next line
      if (currentX + textWidth > x + width && currentX > x) {
        currentX = x;
        currentY += lineHeight;
      }

      // Render background for ability segments
      if (segment.styles.isAbility && segment.styles.backgroundColor) {
        const paddingX = (segment.styles.padding?.x || 0.3) * segmentFontSize;
        const paddingY = (segment.styles.padding?.y || 0.15) * segmentFontSize;
        const bgWidth = textWidth + paddingX * 2;
        const bgHeight = segmentFontSize * 1.3 + paddingY * 2;

        elements.push(
          <Rect
            key={`bg-${index}`}
            x={currentX - paddingX}
            y={currentY - paddingY - segmentFontSize * 0.1}
            width={bgWidth}
            height={bgHeight}
            fill={segment.styles.backgroundColor}
            cornerRadius={segment.styles.borderRadius ? segment.styles.borderRadius * segmentFontSize * 0.1 : 3}
            stroke={segment.styles.border ? segment.styles.border : undefined}
            strokeWidth={segment.styles.border ? 1 : 0}
          />
        );

        // Render text with proper positioning for ability segments
        elements.push(
          <Text
            key={`text-${index}`}
            x={currentX}
            y={currentY}
            text={segment.text}
            fontSize={segmentFontSize}
            fontFamily={fontFamily}
            fill={textColor}
            fontStyle={segment.styles.fontWeight === 'bold' || segment.styles.fontWeight === '600' ? 'bold' : 'normal'}
          />
        );

        currentX += bgWidth + segmentFontSize * 0.2; // Add spacing after ability
      } else {
        // Render regular text
        elements.push(
          <Text
            key={`text-${index}`}
            x={currentX}
            y={currentY}
            text={segment.text}
            fontSize={segmentFontSize}
            fontFamily={fontFamily}
            fill={textColor}
            fontStyle={segment.styles.fontWeight === 'bold' || segment.styles.fontWeight === '600' ? 'bold' : 'normal'}
          />
        );

        currentX += textWidth + segmentFontSize * 0.1; // Add small spacing
      }
    });

    return elements;
  };
  
  return <Group>{renderSegments()}</Group>;
}
