"use client";
import React from "react";
import { Stage, Layer, Text } from "react-konva";
import RichTextRenderer from "./RichTextRenderer";

export default function TestRichText() {
  const testHtml1 = `<p>Blue:<span class="blue-ability">OnPlay</span>Pink:<span class="pink-ability">Once Per Turn</span></p>`;
  const testHtml2 = `<p>Orange: <span class="orange-ability">Blocker</span> Red: <span class="red-ability">Counter</span></p>`;
  const testHtml3 = `<p>Trigger: <span class="trigger-ability">Trigger</span> Black: <span class="black-ability">Don!! ×1</span> White: <span class="white-ability">1</span></p>`;
  const testHtml4 = `<p>Counter: <span class="red-ability">Counter +1000</span> and <span class="blue-ability">OnPlay</span> test</p>`;
  
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Rich Text Renderer Tests</h2>
      
      <div className="space-y-6">
        <div>
          <h3 className="font-semibold mb-2">Test 1: Blue & Pink Abilities</h3>
          <div className="border p-2">
            <Stage width={600} height={80}>
              <Layer>
                <RichTextRenderer
                  htmlContent={testHtml1}
                  x={10}
                  y={20}
                  width={580}
                  fontSize={18}
                  color="#000000"
                />
              </Layer>
            </Stage>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Test 2: Orange & Red Abilities</h3>
          <div className="border p-2">
            <Stage width={600} height={80}>
              <Layer>
                <RichTextRenderer
                  htmlContent={testHtml2}
                  x={10}
                  y={20}
                  width={580}
                  fontSize={18}
                  color="#000000"
                />
              </Layer>
            </Stage>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Test 3: Trigger, Black & White Abilities</h3>
          <div className="border p-2">
            <Stage width={600} height={80}>
              <Layer>
                <RichTextRenderer
                  htmlContent={testHtml3}
                  x={10}
                  y={20}
                  width={580}
                  fontSize={18}
                  color="#000000"
                />
              </Layer>
            </Stage>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Test 4: Counter Icon & Spacing</h3>
          <div className="border p-2">
            <Stage width={600} height={80}>
              <Layer>
                <RichTextRenderer
                  htmlContent={testHtml4}
                  x={10}
                  y={20}
                  width={580}
                  fontSize={18}
                  color="#000000"
                />
              </Layer>
            </Stage>
          </div>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Test 5: No Spaces Test</h3>
          <div className="border p-2">
            <Stage width={600} height={80}>
              <Layer>
                <RichTextRenderer
                  htmlContent="<p>Hello,<span class='blue-ability'>OnPlay</span><span class='pink-ability'>Once Per Turn</span><span class='orange-ability'>Blocker</span><span class='red-ability'>Counter</span></p>"
                  x={10}
                  y={20}
                  width={580}
                  fontSize={18}
                  color="#000000"
                />
              </Layer>
            </Stage>
          </div>
        </div>
      </div>
    </div>
  );
}
