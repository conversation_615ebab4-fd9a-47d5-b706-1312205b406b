"use client";
import React from "react";
import { Stage, Layer, Text } from "react-konva";
import RichTextRenderer from "./RichTextRenderer";

export default function TestRichText() {
  const testHtml1 = `<p>Simple test with <span class="blue-ability">OnPlay</span> ability</p>`;
  const testHtml2 = `<p>Multiple abilities: <span class="blue-ability">OnPlay</span> <span class="pink-ability">Once Per Turn</span></p>`;
  const testHtml3 = `<p>Complex: <span class="orange-ability">Blocker</span> and <span class="red-ability">Counter</span></p>`;
  
  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Rich Text Renderer Tests</h2>
      
      <div className="space-y-6">
        <div>
          <h3 className="font-semibold mb-2">Test 1: Simple ability</h3>
          <div className="border p-2">
            <Stage width={500} height={100}>
              <Layer>
                <Text x={10} y={10} text="Konva Text Test" fontSize={16} fill="black" />
                <RichTextRenderer
                  htmlContent={testHtml1}
                  x={10}
                  y={40}
                  width={480}
                  fontSize={16}
                  color="#000000"
                />
              </Layer>
            </Stage>
          </div>
        </div>
        
        <div>
          <h3 className="font-semibold mb-2">Test 2: Multiple abilities</h3>
          <div className="border p-2">
            <Stage width={500} height={100}>
              <Layer>
                <RichTextRenderer
                  htmlContent={testHtml2}
                  x={10}
                  y={20}
                  width={480}
                  fontSize={16}
                  color="#000000"
                />
              </Layer>
            </Stage>
          </div>
        </div>
        
        <div>
          <h3 className="font-semibold mb-2">Test 3: Complex abilities</h3>
          <div className="border p-2">
            <Stage width={500} height={100}>
              <Layer>
                <RichTextRenderer
                  htmlContent={testHtml3}
                  x={10}
                  y={20}
                  width={480}
                  fontSize={16}
                  color="#000000"
                />
              </Layer>
            </Stage>
          </div>
        </div>
      </div>
    </div>
  );
}
