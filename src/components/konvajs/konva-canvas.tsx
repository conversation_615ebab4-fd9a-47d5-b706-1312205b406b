"use client";
import React from "react";
import { Stage, Layer, Image as KonvaImage, Rect, Group } from "react-konva";
import { useImage } from "react-konva-utils";

const SOURCE = "/assets/Border-Assets/Card-Background.png";

const KonvaCanvas = () => {
  const [nativeImage] = useImage(SOURCE);
  const stageRef = React.useRef<any>(null);

  const downloadImage = (scale: number) => {
    if (!stageRef.current) return;

    const originalWidth = 512;
    const originalHeight = 715.39;

    const dataURL = stageRef.current.toDataURL({
      pixelRatio: scale, // 1x, 2x, 4x
      width: originalWidth,
      height: originalHeight,
    });

    const link = document.createElement("a");
    link.download = `konva-image-${scale}x.png`;
    link.href = dataURL;
    link.click();
  };

  return (
    <div style={{ textAlign: "center" }}>
      <Stage ref={stageRef} width={512} height={715.39}>
        <Layer>
          {nativeImage && (
            <Group x={0}>
              <KonvaImage image={nativeImage} width={512} height={715.39} />
              <Rect
                width={512}
                height={715.39}
                fillLinearGradientStartPoint={{ x: 0, y: 0 }}
                fillLinearGradientEndPoint={{ x: 300, y: 0 }}
                fillLinearGradientColorStops={[
                  0,
                  "rgba(255,0,0,1)",
                  1,
                  "rgba(80,80,255,1)",
                ]}
                globalCompositeOperation="source-in"
              />
            </Group>
          )}
        </Layer>
      </Stage>

      {/* Download buttons */}
      <div style={{ marginTop: "10px" }}>
        <button onClick={() => downloadImage(1)}>Download 1x</button>
        <button onClick={() => downloadImage(2)}>Download 2x</button>
        <button onClick={() => downloadImage(4)}>Download 4x</button>
        <button onClick={() => downloadImage(6.556640625)}>
          Download Print
        </button>
      </div>
    </div>
  );
};

export default KonvaCanvas;
