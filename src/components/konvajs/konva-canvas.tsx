"use client";
import React from "react";
import { Stage, Layer, Image as KonvaImage, Rect, Group } from "react-konva";
import { useImage } from "react-konva-utils";
import RichTextRenderer from "./RichTextRenderer";

const SOURCE = "/assets/Border-Assets/Card-Background.png";

const KonvaCanvas = () => {
  const [nativeImage] = useImage(SOURCE);
  const stageRef = React.useRef<any>(null);

  // Example rich text HTML content
  const exampleHtml = `<p style="text-align: left">Hello, <span style="color: #2F77B3" class="blue-ability">OnPlay</span> <span style="color: #d94880" class="pink-ability">Once Per Turn</span> <span class="orange-ability-container"><span class="orange-ability-shadow"><span style="color: #DC8535" class="orange-ability">Blocker</span></span></span> <span style="color: #ba212f" class="red-ability">Counter</span> <span class="trigger-ability-container"><span class="trigger-ability-shadow"><span style="color: #f8ed70" class="trigger-ability">Trigger</span></span></span> <span class="black-ability-container"><span class="black-ability-shadow"><span style="color: #000000" class="black-ability">Don!! ×1</span></span></span><span style="color: rgb(255, 255, 255)" class=""> </span><span style="color: #FFFFFF" class="white-ability">1</span></p><ul><li><p>some ul with <span style="color: #d94880" class="pink-ability">styles</span></p></li></ul><p>then no ul</p>`;

  console.log('Example HTML:', exampleHtml);

  const downloadImage = (scale: number) => {
    if (!stageRef.current) return;

    const originalWidth = 512;
    const originalHeight = 715.39;

    const dataURL = stageRef.current.toDataURL({
      pixelRatio: scale, // 1x, 2x, 4x
      width: originalWidth,
      height: originalHeight,
    });

    const link = document.createElement("a");
    link.download = `konva-image-${scale}x.png`;
    link.href = dataURL;
    link.click();
  };

  return (
    <div style={{ textAlign: "center" }}>
      <Stage ref={stageRef} width={512} height={715.39}>
        <Layer>
          {nativeImage && (
            <Group x={0}>
              <KonvaImage image={nativeImage} width={512} height={715.39} />
              <Rect
                width={512}
                height={715.39}
                fillLinearGradientStartPoint={{ x: 0, y: 0 }}
                fillLinearGradientEndPoint={{ x: 300, y: 0 }}
                fillLinearGradientColorStops={[
                  0,
                  "rgba(255,0,0,1)",
                  1,
                  "rgba(80,80,255,1)",
                ]}
                globalCompositeOperation="source-in"
              />

              {/* Rich text content overlay */}
              <RichTextRenderer
                htmlContent={exampleHtml}
                x={50}
                y={100}
                width={400}
                fontSize={16}
                fontFamily="Arial"
                color="#1c1917"
              />

              {/* Test with different content */}
              <RichTextRenderer
                htmlContent="<p>Test: <span class='blue-ability'>OnPlay</span> <span class='white-ability'>1</span></p>"
                x={50}
                y={300}
                width={400}
                fontSize={18}
                fontFamily="Arial"
                color="#000000"
              />
            </Group>
          )}
        </Layer>
      </Stage>

      {/* Download buttons */}
      <div style={{ marginTop: "10px" }}>
        <button onClick={() => downloadImage(1)}>Download 1x</button>
        <button onClick={() => downloadImage(2)}>Download 2x</button>
        <button onClick={() => downloadImage(4)}>Download 4x</button>
        <button onClick={() => downloadImage(6.556640625)}>
          Download Print
        </button>
      </div>
    </div>
  );
};

export default KonvaCanvas;
