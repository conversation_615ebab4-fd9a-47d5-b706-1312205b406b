import { DefaultSession } from "next-auth";
import { state } from "@/store/formSlice";
type FormInputType =
  | "color"
  | "donAbility"
  | "don-power"
  | "don-text"
  | "color-leader"
  | "attribute"
  | "name"
  | "card-type"
  | "cost"
  | "costLeader"
  | "power"
  | "ability"
  | "trigger"
  | "counter"
  | "set"
  | "rarity"
  | "rarity2"
  | "card-num"
  | "print-wave"
  | "image-file"
  | "artist"
  | "download"
  | "leader-border-type"
  | "event-border-type"
  | "powerBlack"
  | "foilBorder"
  | "leaderBorderEnabled"
  | "character-border-type"
  | "aaStar"
  | "printReady";

type Color = "red" | "green" | "blue" | "yellow" | "purple" | "black";

type CharacterColor =
  | "red"
  | "green"
  | "blue"
  | "yellow"
  | "purple"
  | "black"
  | "yellowText";

type CardAttribute = "slash" | "wisdom" | "ranged" | "special" | "strike";

type CardCost =
  | "0"
  | "1"
  | "2"
  | "3"
  | "4"
  | "5"
  | "6"
  | "7"
  | "8"
  | "9"
  | "10"
  | "11"
  | "12"
  | "13"
  | "14"
  | "15"
  | "16"
  | "17"
  | "18"
  | "19";

type CardLife =
  | "0"
  | "1"
  | "2"
  | "3"
  | "4"
  | "5"
  | "6"
  | "7"
  | "8"
  | "9"
  | "10";

type LeaderBorder =
  | "standard"
  | "standard-white"
  | "25th"
  | "AA-white"
  | "AA-black"
  | "AA-black-and-white"
  | "rainbow"
  | "full-art";

type EventBorder = "standard" | "eb-02" | "op-10";

type CharacterBorder =
  | "standard"
  | "rare"
  | "sr"
  | "sec"
  | "sec-aa"
  | "mr"
  | "none"
  | "sp-v2";

declare module "next-auth" {
  // Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
  interface Session {
    // A JWT which can be used as Authorization header with supabase-js for RLS.
    supabaseAccessToken?: string;
    user: {
      // the user's postal address
      address: string;
    } & DefaultSession["user"];
  }
}
export type GetCardState = state & {
  cardSubscriptionStatus: string;
  uuid: string;
};

export type TrianglePosition =
  | "top-right"
  | "top-left"
  | "bottom-left"
  | "bottom-right"
  | "left"
  | "right";

export type SubscriptionStatus =
  | "ACTIVE"
  | "CANCELLED"
  | "PAST_DUE"
  | "PAUSED"
  | "LIFETIME"
  | ""
  | null;

export type DisplaySubStatus =
  | "ACTIVE"
  | "CANCELLED"
  | "PAST_DUE"
  | "PAUSED"
  | null;

// https://developer.paddle.com/api-reference/subscriptions/overview
export type PaddleSubscription = {
  data: {
    id: string;
    status: "active" | "canceled" | "paused" | "past_due" | "trialing";
    customer_id: string;
    next_billed_at: string | null;
    paused_at: string | null;
    canceled_at: string | null;
    discount: null | {
      ends_at: string | null;
      id: string;
      starts_at: string | null;
    };
    current_billing_period: null | {
      starts_at: string;
      ends_at: string;
    };
    billing_cycle: {
      frequency: number;
      interval: string;
    };
    scheduled_change: null | {
      action: string;
      effective_at: string;
      resume_at: string | null;
    };
    management_urls: {
      update_payment_method: string | null;
      cancel: string;
    };
    items: Array<SubscriptionItem>;
    custom_data: object | null;
  };
};

type SubscriptionItem = {
  product: {
    name: string;
  };
  price: {
    name: string;
  };
};

type TransactionItem = {
  price: {
    name: string;
  };
};

export type PaddleTransaction = {
  data: {
    id: string;
    status: string;
    created_at: string;
    customer_id: string;
    subscription_id: string;
    items: Array<TransactionItem>;
  };
};

export type Subscription = {
  lifetime?: boolean | undefined;
  active: boolean;
  subscriptionName: string;
  subscriptionPlanName: string;
  updatePaymentMethod: string | null;
  id?: string | undefined;
  status:
    | "active"
    | "canceled"
    | "paused"
    | "past_due"
    | "trialing"
    | undefined;
  scheduledChange: null | {
    action: string;
    effective_at: string;
    resume_at: string | null;
  };
  cardTokens: number | null | undefined;
};
